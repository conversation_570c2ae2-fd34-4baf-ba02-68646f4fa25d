from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import HTTPException, RequestValidationError
from pydantic import ValidationError
from pydantic_validation_decorator import FieldValidationError
from exceptions.exception import (
    AuthException,
    LoginException,
    ModelValidatorException,
    PermissionException,
    ServiceException,
    ServiceWarning,
)
from utils.log_util import logger
from utils.response_util import jsonable_encoder, JSONResponse, ResponseUtil


def handle_exception(app: FastAPI):
    """
    全局异常处理
    """

    # 自定义token检验异常
    @app.exception_handler(AuthException)
    async def auth_exception_handler(request: Request, exc: AuthException):
        return ResponseUtil.unauthorized(data=exc.data, msg=exc.message)

    # 自定义登录检验异常
    @app.exception_handler(LoginException)
    async def login_exception_handler(request: Request, exc: LoginException):
        return ResponseUtil.failure(data=exc.data, msg=exc.message)

    # 自定义模型检验异常
    @app.exception_handler(ModelValidatorException)
    async def model_validator_exception_handler(request: Request, exc: ModelValidatorException):
        logger.warning(exc.message)
        return ResponseUtil.failure(data=exc.data, msg=exc.message)

    # 自定义字段检验异常
    @app.exception_handler(FieldValidationError)
    async def field_validation_error_handler(request: Request, exc: FieldValidationError):
        logger.warning(exc.message)
        return ResponseUtil.failure(msg=exc.message)

    # FastAPI请求验证异常（422错误）
    @app.exception_handler(RequestValidationError)
    async def request_validation_exception_handler(request: Request, exc: RequestValidationError):
        """
        处理FastAPI请求验证错误（422状态码）
        """
        error_details = []
        for error in exc.errors():
            field_name = " -> ".join(str(loc) for loc in error["loc"])
            error_msg = error["msg"]
            error_details.append(f"{field_name}: {error_msg}")

        error_message = "请求参数验证失败: " + "; ".join(error_details)
        logger.warning(f"请求验证失败: {error_message}")

        return ResponseUtil.failure(
            msg=error_message,
            data={"validation_errors": exc.errors()}
        )

    # Pydantic验证异常
    @app.exception_handler(ValidationError)
    async def validation_exception_handler(request: Request, exc: ValidationError):
        """
        处理Pydantic验证错误
        """
        error_details = []
        for error in exc.errors():
            field_name = " -> ".join(str(loc) for loc in error["loc"])
            error_msg = error["msg"]
            error_details.append(f"{field_name}: {error_msg}")

        error_message = "数据验证失败: " + "; ".join(error_details)
        logger.warning(f"数据验证失败: {error_message}")

        return ResponseUtil.failure(
            msg=error_message,
            data={"validation_errors": exc.errors()}
        )

    # 自定义权限检验异常
    @app.exception_handler(PermissionException)
    async def permission_exception_handler(request: Request, exc: PermissionException):
        return ResponseUtil.forbidden(data=exc.data, msg=exc.message)

    # 自定义服务异常
    @app.exception_handler(ServiceException)
    async def service_exception_handler(request: Request, exc: ServiceException):
        logger.error(exc.message)
        return ResponseUtil.error(data=exc.data, msg=exc.message)

    # 自定义服务警告
    @app.exception_handler(ServiceWarning)
    async def service_warning_handler(request: Request, exc: ServiceWarning):
        logger.warning(exc.message)
        return ResponseUtil.failure(data=exc.data, msg=exc.message)

    # 处理其他http请求异常
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return JSONResponse(
            content=jsonable_encoder({'code': exc.status_code, 'msg': exc.detail}), status_code=exc.status_code
        )

    # 处理数据库连接异常
    @app.exception_handler(Exception)
    async def exception_handler(request: Request, exc: Exception):
        # 检查是否是数据库连接相关的错误
        error_str = str(exc)
        error_type = type(exc).__name__

        # 数据库连接错误的特殊处理
        if "Command Out of Sync" in error_str or "OperationalError" in error_type:
            logger.error(f"数据库连接错误: {exc}")
            return ResponseUtil.error(
                msg="数据库连接异常，请稍后重试。如果问题持续存在，请联系系统管理员。",
                data={"error_type": "database_connection_error", "details": "数据库连接状态不一致"}
            )
        elif "Connection" in error_str and ("timeout" in error_str.lower() or "refused" in error_str.lower()):
            logger.error(f"数据库连接超时或拒绝: {exc}")
            return ResponseUtil.error(
                msg="数据库连接超时，请检查网络连接或稍后重试。",
                data={"error_type": "database_timeout_error"}
            )
        elif "Lost connection" in error_str or "MySQL server has gone away" in error_str:
            logger.error(f"数据库连接丢失: {exc}")
            return ResponseUtil.error(
                msg="数据库连接已断开，系统正在尝试重新连接，请稍后重试。",
                data={"error_type": "database_connection_lost"}
            )
        else:
            # 其他异常的通用处理
            logger.exception(exc)
            return ResponseUtil.error(msg="系统内部错误，请联系管理员。")
