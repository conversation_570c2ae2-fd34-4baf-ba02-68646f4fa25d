2025-07-28 13:57:21.259 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 13:57:21.259 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 13:57:21.286 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 13:57:21.286 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 13:57:21.288 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 13:57:21.320 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 13:57:21.343 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 13:57:21.344 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 13:57:37.356 | d848e600fbf84d7f98f4e047df4a4165 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 13:57:37.370 | b8e9f32137d34d81b10bdd7f583f4f82 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 13:57:37.537 | a8808dc4991449809dc3c0c34de37e3f | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 13:58:33.260 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 13:58:33.260 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 13:58:33.260 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 13:58:33.261 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 13:58:33.261 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 13:58:33.261 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 13:58:33.261 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 13:58:33.262 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 13:58:33.262 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 13:58:33.262 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 13:58:33.268 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 13:58:33.268 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 13:58:33.268 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 13:58:33.268 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 13:58:33.270 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 13:58:33.271 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 13:58:33.271 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 13:58:33.271 | 30897fe6275543b5a30c6eb87e355109 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 13:58:34.875 | e55bd0cda808408680e53bcc81accdbd | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 13:58:34.876 | e55bd0cda808408680e53bcc81accdbd | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 13:58:34.884 | e55bd0cda808408680e53bcc81accdbd | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 13:58:36.209 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 13:58:36.209 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 13:58:36.209 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 13:58:36.209 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 13:58:36.209 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 13:58:36.210 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 13:58:36.210 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 13:58:36.210 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 13:58:36.210 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 13:58:36.213 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 13:58:36.213 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 13:58:36.214 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 13:58:36.214 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 13:58:36.215 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 13:58:36.215 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 13:58:36.216 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 13:58:36.216 | e24ac6a34db843f99a75f2823d72f534 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 13:58:37.168 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 13:58:37.169 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 13:58:37.169 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 13:58:37.169 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 13:58:37.169 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 13:58:37.169 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 13:58:37.170 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 13:58:37.170 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 13:58:37.170 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 13:58:37.170 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 13:58:37.175 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 13:58:37.175 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 13:58:37.175 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 13:58:37.176 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 13:58:37.177 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 13:58:37.177 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 13:58:37.177 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 13:58:37.177 | 1aaecda000cd473980e13131fa1f3e44 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 13:58:40.840 | 219709b8ba2c419bbe7829acfacf9d94 | INFO     | module_stream.controller.stream_controller:test_rtsp_connection:412 - 用户 admin 测试RTSP连接: rtsp://127.0.0.1:8554/test1
2025-07-28 13:58:52.058 | 219709b8ba2c419bbe7829acfacf9d94 | INFO     | module_stream.controller.stream_controller:test_rtsp_connection:417 - RTSP连接测试成功: rtsp://127.0.0.1:8554/test1
2025-07-28 13:58:57.914 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 13:58:57.914 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 13:58:57.914 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 13:58:57.914 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 13:58:57.915 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 13:58:57.915 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 13:58:57.915 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 13:58:57.916 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 13:58:57.916 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 13:58:57.919 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 13:58:57.919 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 13:58:57.919 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 13:58:57.919 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 13:58:57.921 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 13:58:57.921 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 13:58:57.921 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 13:58:57.921 | ab15ed1c89104fddaac876154999dfb4 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 13:58:58.983 | dd0bcea0f99b4c76b51ddc4798396585 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 13:59:45.667 | 4de5df86ca04485986cbe7fbb613aa75 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 13:59:45.683 | 229aef2945834195a97ff5e3fa0ab93c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 13:59:45.823 | e8330af28e934560a3df53d701fbe30f | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 14:01:12.232 | e89449c7edf240869122b01b17dd1ad1 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 14:01:29.509 | b9fe099ea8f948ab841a787f35042c3f | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 14:01:40.955 | dc7af6f6e129458ebd6793d592cc7a43 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 14:01:54.535 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 14:01:54.535 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
