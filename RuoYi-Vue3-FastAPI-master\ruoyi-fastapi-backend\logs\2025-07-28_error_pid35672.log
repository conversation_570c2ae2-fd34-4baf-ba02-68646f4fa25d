2025-07-28 10:55:00.672 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 10:55:00.673 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 10:55:00.702 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 10:55:00.703 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 10:55:00.704 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 10:55:00.738 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 10:55:00.822 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 10:55:00.822 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 10:55:17.643 | 07b1896a940b4e60bd3a301161b8f896 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:55:17.657 | ac321e22b8af4387a5970e901d7ee12b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:55:22.399 | 8b4c30fc2e104b93be9805869f002466 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:59:07.242 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 10:59:07.242 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
