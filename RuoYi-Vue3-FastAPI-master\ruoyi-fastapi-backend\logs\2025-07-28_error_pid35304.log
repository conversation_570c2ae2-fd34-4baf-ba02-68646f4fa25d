2025-07-28 12:19:06.287 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:19:06.287 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:19:06.319 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:19:06.320 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:19:06.321 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:19:06.360 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:19:06.371 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:19:06.372 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:19:12.316 | 2a7ecf336c2f4bf4a500e7ed6132c34d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 12:19:12.342 | 403abbf6688c48c59e0b150056c6471e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 12:19:12.503 | 8022944840b54514ae04494bd47732fb | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:20:23.893 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:20:23.893 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
