2025-07-28 15:03:17.011 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 15:03:17.012 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 15:03:17.048 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 15:03:17.049 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 15:03:17.050 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 15:03:17.096 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 15:03:17.110 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 15:03:17.110 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 15:03:18.662 | 849d21d058884a5db69e8a3cfc08793c | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-07-28 15:03:18.670 | d329ee142585452082863a141e4aad7a | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-28 15:03:18.762 | 89e7748e79d84d798d0ba42e35a1882d | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为1121d19b-92b8-4e15-9319-d7a8c37d9210的会话获取图片验证码成功
2025-07-28 15:03:22.096 | be8493253270420a99f5c022d594a10c | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-28 15:03:22.118 | 593ff1257b0b4541bc86bfc906e697b7 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 15:03:22.132 | 76a37f97496a4bedbacfd24d9ae233d9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 15:03:25.365 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 15:03:25.366 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:03:25.366 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:03:25.366 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:03:25.366 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:03:25.366 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:03:25.366 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:03:25.367 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:03:25.367 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:03:25.367 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:03:25.392 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:03:25.392 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:03:25.392 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:03:25.392 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:03:25.394 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:03:25.395 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:03:25.395 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:03:25.395 | 2aae6fab707e4bcbb5f2488fb92a8c33 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 15:03:26.698 | 04e0214b6835495baa00d9e4a845e532 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 15:03:26.699 | 04e0214b6835495baa00d9e4a845e532 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 15:03:26.704 | 04e0214b6835495baa00d9e4a845e532 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 15:03:28.192 | a05455ce717144129de04b76d8d1aad6 | INFO     | module_stream.controller.stream_controller:test_rtsp_connection:412 - 用户 admin 测试RTSP连接: rtsp://127.0.0.1:8554/test2
2025-07-28 15:03:28.265 | a05455ce717144129de04b76d8d1aad6 | WARNING  | module_stream.controller.stream_controller:test_rtsp_connection:420 - RTSP连接测试失败: rtsp://127.0.0.1:8554/test2, 错误: 无法连接到视频流，请检查RTSP地址是否正确
2025-07-28 15:03:59.781 | 7bb336ee6473495eaf400e3e140ded42 | INFO     | module_stream.controller.stream_controller:test_rtsp_connection:412 - 用户 admin 测试RTSP连接: rtsp://127.0.0.1:8554/test2
2025-07-28 15:03:59.836 | 7bb336ee6473495eaf400e3e140ded42 | WARNING  | module_stream.controller.stream_controller:test_rtsp_connection:420 - RTSP连接测试失败: rtsp://127.0.0.1:8554/test2, 错误: 无法连接到视频流，请检查RTSP地址是否正确
2025-07-28 15:04:03.465 | ff92467ec59647dca6d561d6fff3da9d | INFO     | module_stream.controller.stream_controller:test_rtsp_connection:412 - 用户 admin 测试RTSP连接: rtsp://127.0.0.1:8554/test1
2025-07-28 15:04:18.146 | ff92467ec59647dca6d561d6fff3da9d | INFO     | module_stream.controller.stream_controller:test_rtsp_connection:417 - RTSP连接测试成功: rtsp://127.0.0.1:8554/test1
2025-07-28 15:04:24.566 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 15:04:24.567 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:04:24.567 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:04:24.567 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:04:24.567 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:04:24.568 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:04:24.568 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:04:24.568 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:04:24.568 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:04:24.568 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:04:24.573 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:04:24.573 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:04:24.573 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:04:24.573 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:04:24.575 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:04:24.576 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:04:24.576 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:04:24.577 | f36b2942c3034d21a5d18bad94d9570a | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 15:04:28.634 | 63a5108260e24006a3399216b3f84092 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:04:30.081 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:04:30.082 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:04:30.082 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 15:04:30.082 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:04:30.082 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:04:30.082 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:04:30.082 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 15:04:30.082 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:04:30.083 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:04:30.086 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:04:30.086 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:04:30.086 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:04:30.087 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:04:30.088 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:04:30.088 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:04:30.088 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:04:30.089 | a094895e2a2349d0b1f31c82001a3c99 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 15:04:31.891 | 9ea540d82bd3404993906dd16517c5ad | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:04:34.779 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1094: 实体对象 = True
2025-07-28 15:04:34.780 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142846_665.jpg
2025-07-28 15:04:34.780 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142846_665.jpg
2025-07-28 15:04:34.782 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1094
2025-07-28 15:04:34.785 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1093: 实体对象 = True
2025-07-28 15:04:34.785 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142844_435.jpg
2025-07-28 15:04:34.785 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142844_435.jpg
2025-07-28 15:04:34.787 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1093
2025-07-28 15:04:34.789 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1092: 实体对象 = True
2025-07-28 15:04:34.789 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142842_160.jpg
2025-07-28 15:04:34.789 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142842_160.jpg
2025-07-28 15:04:34.791 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1092
2025-07-28 15:04:34.792 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1091: 实体对象 = True
2025-07-28 15:04:34.793 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142839_921.jpg
2025-07-28 15:04:34.793 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142839_921.jpg
2025-07-28 15:04:34.794 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1091
2025-07-28 15:04:34.796 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1090: 实体对象 = True
2025-07-28 15:04:34.797 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142837_570.jpg
2025-07-28 15:04:34.797 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142837_570.jpg
2025-07-28 15:04:34.798 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1090
2025-07-28 15:04:34.799 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1089: 实体对象 = True
2025-07-28 15:04:34.800 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142835_246.jpg
2025-07-28 15:04:34.800 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142835_246.jpg
2025-07-28 15:04:34.802 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1089
2025-07-28 15:04:34.815 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1088: 实体对象 = True
2025-07-28 15:04:34.816 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142832_962.jpg
2025-07-28 15:04:34.816 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142832_962.jpg
2025-07-28 15:04:34.828 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1088
2025-07-28 15:04:34.829 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1087: 实体对象 = True
2025-07-28 15:04:34.830 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142830_703.jpg
2025-07-28 15:04:34.830 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142830_703.jpg
2025-07-28 15:04:34.831 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1087
2025-07-28 15:04:34.833 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1086: 实体对象 = True
2025-07-28 15:04:34.834 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142828_511.jpg
2025-07-28 15:04:34.834 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142828_511.jpg
2025-07-28 15:04:34.841 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1086
2025-07-28 15:04:34.843 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1085: 实体对象 = True
2025-07-28 15:04:34.843 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142826_325.jpg
2025-07-28 15:04:34.844 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142826_325.jpg
2025-07-28 15:04:34.845 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1085
2025-07-28 15:04:34.848 | c983f4c61ee041ddadd2a8c924c934eb | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 15:04:34.890 | 30a97a100da745aa9b4924732ee97208 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:04:37.361 | 7798e907490549229fd89c48309c51ab | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1084: 实体对象 = True
2025-07-28 15:04:37.361 | 7798e907490549229fd89c48309c51ab | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_142824_146.jpg
2025-07-28 15:04:37.362 | 7798e907490549229fd89c48309c51ab | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_142824_146.jpg
2025-07-28 15:04:37.363 | 7798e907490549229fd89c48309c51ab | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1084
2025-07-28 15:04:37.371 | 7798e907490549229fd89c48309c51ab | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除1条记录，删除1个截图文件
2025-07-28 15:04:37.404 | dac683368dad4979a0e30eb3b1c6f630 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:04:59.526 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 15:04:59.526 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
