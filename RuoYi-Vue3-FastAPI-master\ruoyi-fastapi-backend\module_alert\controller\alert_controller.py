from datetime import datetime
from fastapi import APIRouter, Depends, Form, Request, Query
from pydantic_validation_decorator import <PERSON><PERSON><PERSON><PERSON><PERSON>s
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_alert.service.alert_service import AlertService
from module_alert.entity.vo.alert_vo import DeleteAlertModel, AlertModel, AlertPageQueryModel, AlertStatusUpdateModel
from utils.common_util import bytes2file_response
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil
from utils.log_util import logger


alertController = APIRouter(prefix='/surveillance/alert', dependencies=[Depends(LoginService.get_current_user)])


@alertController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('surveillance:alert:list'))]
)
async def get_alert_manage_alert_list(
    request: Request,
    pageNum: int = Query(1, description='当前页码'),
    pageSize: int = Query(10, description='每页记录数'),
    alertType: Optional[str] = Query(None, description='警告类型'),
    alertLevel: Optional[str] = Query(None, description='警告等级'),
    algorithmType: Optional[str] = Query(None, description='算法类型'),
    status: Optional[str] = Query(None, description='处理状态'),
    alertTime: Optional[str] = Query(None, description='告警时间范围，格式：开始日期,结束日期'),
    streamName: Optional[str] = Query(None, description='数据流名称'),
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    # 解析时间范围
    begin_time = None
    end_time = None
    if alertTime:
        try:
            # 前端发送的格式可能是 "2024-01-01,2024-01-31" 或者数组格式
            if ',' in alertTime:
                time_parts = alertTime.split(',')
                if len(time_parts) == 2:
                    begin_time = time_parts[0].strip()
                    end_time = time_parts[1].strip()
        except Exception as e:
            logger.warning(f"解析时间范围失败: {alertTime}, 错误: {e}")

    # 构建查询参数对象
    alert_page_query = AlertPageQueryModel(
        pageNum=pageNum,
        pageSize=pageSize,
        userId=current_user.user.user_id,  # 添加当前用户ID
        alertType=alertType,
        alertLevel=alertLevel,
        algorithmType=algorithmType,
        status=status,  # 使用status而不是isHandled
        beginTime=begin_time,
        endTime=end_time,
        streamName=streamName  # 添加数据流名称筛选
    )

    # 获取分页数据
    alert_page_query_result = await AlertService.get_alert_list_services(query_db, alert_page_query, is_page=True)
    logger.info('获取成功')

    return ResponseUtil.success(model_content=alert_page_query_result)


@alertController.post('', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:alert:add'))])
@ValidateFields(validate_model='add_alert')
@Log(title='警告记录', business_type=BusinessType.INSERT)
async def add_alert_manage_alert(
    request: Request,
    add_alert: AlertModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    add_alert.create_time = datetime.now()
    add_alert_result = await AlertService.add_alert_services(query_db, add_alert)
    logger.info(add_alert_result.message)

    return ResponseUtil.success(msg=add_alert_result.message)


@alertController.put('', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:alert:edit'))])
@ValidateFields(validate_model='edit_alert')
@Log(title='警告记录', business_type=BusinessType.UPDATE)
async def edit_alert_manage_alert(
    request: Request,
    edit_alert: AlertModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    edit_alert.update_by = current_user.user.user_name
    edit_alert.update_time = datetime.now()
    edit_alert_result = await AlertService.edit_alert_services(query_db, edit_alert)
    logger.info(edit_alert_result.message)

    return ResponseUtil.success(msg=edit_alert_result.message)


@alertController.delete('/{alert_ids}', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:alert:remove'))])
@Log(title='警告记录', business_type=BusinessType.DELETE)
async def delete_alert_manage_alert(
    request: Request,
    alert_ids: str,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    delete_alert = DeleteAlertModel(alertIds=alert_ids)
    delete_alert_result = await AlertService.delete_alert_services(query_db, delete_alert, current_user.user.user_id)
    logger.info(delete_alert_result.message)

    return ResponseUtil.success(msg=delete_alert_result.message)


@alertController.put('/status', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:alert:edit'))])
@Log(title='警告记录状态', business_type=BusinessType.UPDATE)
async def update_alert_status(
    request: Request,
    status_update: AlertStatusUpdateModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新告警处理状态
    """
    update_result = await AlertService.update_alert_status_services(
        query_db, status_update, current_user.user.user_name
    )
    logger.info(update_result.message)

    return ResponseUtil.success(msg=update_result.message)


@alertController.get(
    '/{alert_id}', response_model=AlertModel, dependencies=[Depends(CheckUserInterfaceAuth('surveillance:alert:query'))]
)
async def query_detail_alert_manage_alert(
    request: Request,
    alert_id: int,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    alert_detail_result = await AlertService.alert_detail_services(query_db, alert_id, current_user.user.user_id)
    logger.info(f'获取alert_id为{alert_id}的信息成功')

    return ResponseUtil.success(data=alert_detail_result)


@alertController.post('/export', dependencies=[Depends(CheckUserInterfaceAuth('surveillance:alert:export'))])
@Log(title='警告记录', business_type=BusinessType.EXPORT)
async def export_alert_manage_alert_list(
    request: Request,
    alert_page_query: AlertPageQueryModel = Form(),
    query_db: AsyncSession = Depends(get_db),
):
    # 获取全量数据
    alert_query_result = await AlertService.get_alert_list_services(query_db, alert_page_query, is_page=False)
    alert_export_result = await AlertService.export_alert_list_services(alert_query_result)
    logger.info('导出成功')

    return ResponseUtil.streaming(data=bytes2file_response(alert_export_result))
