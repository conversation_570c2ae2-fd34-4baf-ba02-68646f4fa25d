from sqlalchemy import Column, Integer, BigInteger, String, DateTime, Text, Float, Index
from config.database import Base


class SurveillanceAlert(Base):
    """
    监控告警记录表
    """
    __tablename__ = 'surveillance_alert'

    alert_id = Column(Integer, primary_key=True, autoincrement=True, comment='告警ID')
    task_id = Column(BigInteger, nullable=False, comment='任务ID')
    stream_id = Column(BigInteger, nullable=False, comment='视频流ID')
    alert_type = Column(String(100), nullable=False, comment='告警类型')
    alert_level = Column(String(10), nullable=False, default='1', comment='告警级别：1-低，2-中，3-高')
    alert_message = Column(String(500), nullable=False, comment='告警消息')
    alert_time = Column(DateTime, nullable=False, comment='告警时间')
    screenshot_path = Column(String(500), comment='告警截图路径')
    bbox_info = Column(Text, comment='检测框信息（JSON格式）')
    confidence = Column(Float, default=0.0, comment='检测置信度')
    status = Column(String(10), nullable=False, default='0', comment='处理状态：0-未处理，1-已处理，2-已忽略')
    handle_user = Column(String(50), comment='处理人')
    handle_time = Column(DateTime, comment='处理时间')
    handle_remark = Column(String(500), comment='处理备注')

    # 通用字段
    create_by = Column(String(50), comment='创建者')
    create_time = Column(DateTime, comment='创建时间')
    update_by = Column(String(50), comment='更新者')
    update_time = Column(DateTime, comment='更新时间')
    remark = Column(String(500), comment='备注')
    del_flag = Column(String(1), nullable=False, default='0', comment='删除标志：0-正常，1-删除')

    # 创建索引
    __table_args__ = (
        Index('idx_task_id', 'task_id'),
        Index('idx_stream_id', 'stream_id'),
        Index('idx_alert_time', 'alert_time'),
        Index('idx_alert_type', 'alert_type'),
        Index('idx_status', 'status'),
        {'comment': '监控告警记录表'}
    )



