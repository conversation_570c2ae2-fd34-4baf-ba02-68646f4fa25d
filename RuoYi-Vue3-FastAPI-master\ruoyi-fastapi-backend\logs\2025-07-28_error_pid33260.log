2025-07-28 10:35:03.610 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 10:35:03.611 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 10:35:03.637 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 10:35:03.638 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 10:35:03.639 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 10:35:03.671 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 10:35:03.699 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 10:35:03.699 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 10:35:31.368 | 7d5588780b5f4959b00eaf3317b20db4 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:35:31.382 | 4edbef8e9c9d421697c167db2c5e3c24 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:35:31.525 | 31e9367e3c364efd874fce59a14648fc | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:36:13.880 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 10:36:13.881 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 10:36:13.881 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 10:36:13.881 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 10:36:13.881 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 10:36:13.882 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 10:36:13.882 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 10:36:13.882 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 10:36:13.882 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 10:36:13.887 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 10:36:13.887 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 10:36:13.887 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 10:36:13.887 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 10:36:13.889 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 10:36:13.891 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 10:36:13.891 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 10:36:13.891 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 10:36:13.891 | 2f651269d3914d13b62704d8a61ef9f9 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 10:36:16.604 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 10:36:16.604 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 10:36:16.605 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 10:36:16.605 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 10:36:16.605 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 10:36:16.605 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 10:36:16.606 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 10:36:16.606 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 10:36:16.606 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 10:36:16.608 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 10:36:16.612 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 10:36:16.612 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 10:36:16.612 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 10:36:16.612 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 10:36:16.613 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 10:36:16.615 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 10:36:16.615 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 10:36:16.615 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 10:36:16.615 | 88eaee15d8f34a4d971b5660a7e9ab69 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 10:36:25.503 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:start_task:201 - 开始启动任务: 12
2025-07-28 10:36:25.507 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:470 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 10:36:25.507 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:477 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 10:36:25.507 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:484 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 10:36:25.507 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:491 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 10:36:25.507 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:497 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 10:36:25.507 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 10:36:25.507 | c000a5bc9c3b47e79151abc349da0e11 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:612 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 10:36:25.508 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:636 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 10:36:25.508 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:651 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 10:36:25.509 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:656 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 10:36:25.509 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:657 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 10:36:25.509 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:661 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 10:36:26.825 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:682 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 10:36:26.825 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:563 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 10:36:26.825 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:563 - 从model_params获取 nms_threshold: 0.5
2025-07-28 10:36:26.825 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:563 - 从model_params获取 input_size: 640
2025-07-28 10:36:26.825 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 10:36:26.826 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:694 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 10:36:29.336 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:706 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 10:36:29.336 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:707 -    - 设备: cuda
2025-07-28 10:36:29.337 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:708 -    - 图像尺寸: 640
2025-07-28 10:36:29.337 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:709 -    - 置信度阈值: 0.01
2025-07-28 10:36:29.337 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 -    - NMS阈值: 0.5
2025-07-28 10:36:29.339 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:723 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 10:36:29.355 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3475 - 任务 12 的监控流已启动
2025-07-28 10:36:29.357 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_cache_task_config:109 - 任务12配置已缓存
2025-07-28 10:36:29.357 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:771 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 10:36:29.358 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:801 - 成功预导入YOLOv5 utils模块
2025-07-28 10:36:29.358 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:807 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 10:36:29.359 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:823 - 重新加载模块: zql_detect
2025-07-28 10:36:29.360 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:823 - 重新加载模块: model
2025-07-28 10:36:29.360 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:827 - 成功导入智驱力模型
2025-07-28 10:36:29.360 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:563 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 10:36:29.360 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:563 - 从model_params获取 nms_threshold: 0.5
2025-07-28 10:36:29.360 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:563 - 从model_params获取 input_size: 640
2025-07-28 10:36:29.360 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:450 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 10:36:29.360 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:839 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 10:36:29.503 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:852 - 智驱力模型初始化成功
2025-07-28 10:36:29.504 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:853 -    - 设备: cuda
2025-07-28 10:36:29.504 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:854 -    - 图像尺寸: 640
2025-07-28 10:36:29.504 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:855 -    - 置信度阈值: 0.01
2025-07-28 10:36:29.504 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:856 -    - NMS阈值: 0.5
2025-07-28 10:36:29.504 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:detection_loop:869 - 智驱力后处理器初始化成功
2025-07-28 10:36:29.572 | c000a5bc9c3b47e79151abc349da0e11 | ERROR    | module_stream.service.task_execution_service:detection_loop:889 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-28 10:36:29.574 | c000a5bc9c3b47e79151abc349da0e11 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3032 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-28 10:36:29.575 | c000a5bc9c3b47e79151abc349da0e11 | ERROR    | module_stream.service.task_execution_service:detection_loop:1231 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-28 10:36:29.575 | c000a5bc9c3b47e79151abc349da0e11 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3032 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-28 10:36:29.577 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.service.task_execution_service:start_task:252 - 任务 12 启动成功，包括实时监控流
2025-07-28 10:36:29.578 | c000a5bc9c3b47e79151abc349da0e11 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 10:36:29.590 | 740a347f63d345dbb76d2d0f702daba9 | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 10:38:51.187 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 10:38:51.188 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
