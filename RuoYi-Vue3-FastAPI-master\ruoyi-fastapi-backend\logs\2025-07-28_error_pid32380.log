2025-07-28 10:22:30.756 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 10:22:30.757 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 10:22:30.782 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 10:22:30.782 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 10:22:30.784 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 10:22:30.817 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 10:22:30.852 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 10:22:30.852 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 10:22:36.746 | 37fbb621137e495bb2c578c37db51079 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:22:36.761 | 86cfe68221194857ba5acf698aa4a55d | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:22:36.893 | d927ef6f17914b51a69f3c9a869216fd | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:68 - 获取成功
2025-07-28 10:23:06.456 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 10:23:06.456 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
