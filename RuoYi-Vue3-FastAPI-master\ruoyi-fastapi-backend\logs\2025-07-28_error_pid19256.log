2025-07-28 10:24:54.496 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 10:24:54.497 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 10:24:54.522 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 10:24:54.522 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 10:24:54.523 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 10:24:54.556 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 10:24:54.584 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 10:24:54.584 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 10:25:44.240 | 143da59b9b9340958acb0ab82672c661 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:25:44.255 | ec3f119bcdb640eaa1cdf63d491fd137 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:25:54.162 | 7bdbe7bccad54be0a0e39dae7aa3e46a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:25:54.177 | d036f3f47c62476885f1fdf209a476ee | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:25:54.339 | 1f1732c739004a1d8dd4fb4754526be7 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:25:58.825 | 0c4398c0bf1343a2b4c295a71bd39ef3 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:25:58.841 | 64dfe25b074e42dc86322b8fcaf79f2c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:25:58.952 | 50a17b29ac074dcaa0143fff4a34addc | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:26:14.787 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 10:26:14.787 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
