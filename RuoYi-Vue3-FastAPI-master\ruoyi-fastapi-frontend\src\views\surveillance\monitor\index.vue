<template>
  <div class="app-container">
    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Refresh"
          @click="refreshMonitor"
          :loading="loading"
        >刷新</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="VideoPause"
          @click="stopAllTasks"
          :disabled="runningTasks.length === 0"
        >停止全部</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Setting"
          @click="goToTaskManage"
        >任务管理</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 搜索栏 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 监控网格 -->
    <div class="monitor-grid" v-loading="loading">
      <div 
        v-for="task in runningTasks" 
        :key="task.taskId"
        class="monitor-item"
        :class="{ 'running': task.status === '1', 'stopped': task.status === '0', 'paused': task.status === '2' }"
      >
        <!-- 视频显示区域 -->
        <div class="video-container">
          <div class="video-header">
            <span class="task-name">{{ task.taskName }}</span>
            <el-tag 
              :type="getStatusTagType(task.status)" 
              size="small"
            >
              {{ task.statusText }}
            </el-tag>
          </div>
          
          <!-- 视频画面 -->
          <div class="video-content">
            <img
              v-if="task.currentFrame && task.status === '1'"
              :src="task.currentFrame"
              :alt="task.taskName"
              class="video-frame"
              @error="handleImageError(task)"
              :key="`${task.taskId}-${Date.now()}`"
            />
            <div v-else class="no-video">
              <el-icon size="48"><VideoCamera /></el-icon>
              <p>{{ getVideoStatusText(task) }}</p>
            </div>

            <!-- 检测结果指示器 -->
            <div v-if="task.status === '1' && task.hasDetection" class="detection-indicator">
              <el-tag type="warning" size="small">
                <el-icon><Warning /></el-icon>
                检测到目标
              </el-tag>
            </div>

            <!-- 告警指示器 -->
            <div v-if="task.status === '1' && task.hasAlert" class="alert-indicator">
              <el-tag type="danger" size="small">
                <el-icon><Bell /></el-icon>
                告警
              </el-tag>
            </div>
          </div>

          <!-- 控制按钮 -->
          <div class="video-controls">
            <el-button-group size="small">
              <el-button
                type="danger"
                icon="VideoStop"
                @click="stopTask(task)"
                :loading="task.stopping"
                :disabled="task.stopping"
              >
                停止
              </el-button>
              <el-button
                type="primary"
                icon="Setting"
                @click="goToTaskManage"
              >
                任务管理
              </el-button>
            </el-button-group>
          </div>

          <!-- 任务信息 -->
          <div class="task-info">
            <div class="info-row">
              <span>位置：{{ task.streamLocation || '未设置' }}</span>
            </div>
            <div class="info-row">
              <span>算法：{{ task.algorithmName || '未配置' }}</span>
            </div>
            <div class="info-row">
              <span>运行次数：{{ task.runCount || 0 }}</span>
              <span>告警次数：{{ task.alertCount || 0 }}</span>
            </div>
            <div class="info-row">
              <span>最后运行：{{ formatTime(task.lastRunTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="runningTasks.length === 0" class="empty-state">
        <el-empty description="暂无运行中的监控任务">
          <el-button type="primary" @click="goToTaskManage">
            去启动任务
          </el-button>
        </el-empty>
      </div>
    </div>



  </div>
</template>

<script setup name="Monitor">
import { stopTaskUnified } from "@/api/stream_manage/task";
import { listMonitorTasks, batchStopTasks } from "@/api/surveillance/monitor";
import { ElNotification } from 'element-plus';

const { proxy } = getCurrentInstance();

const runningTasks = ref([]);
const loading = ref(true);
const showSearch = ref(true);

// WebSocket监控管理
const monitorWebSockets = ref(new Map()); // 存储每个任务的WebSocket连接

const queryParams = ref({
  taskName: null,
  status: null,
  pageNum: 1,
  pageSize: 100  // 获取更多任务用于监控
});



// 定时器
let frameUpdateTimer = null;

/** 查询任务列表 */
function getList() {
  loading.value = true;
  console.log('🔍 开始获取监控任务列表...');

  listMonitorTasks(queryParams.value).then(response => {
    console.log('✅ 获取任务列表成功:', response);

    // 只显示运行中的任务（status === '1'）
    const allTasks = response.rows || [];
    const runningTasksFiltered = allTasks.filter(task => task.status === '1');

    console.log(`📊 任务统计: 总任务数=${allTasks.length}, 运行中任务数=${runningTasksFiltered.length}`);

    runningTasks.value = runningTasksFiltered.map(task => ({
      ...task,
      currentFrame: null,
      stopping: false,
      hasDetection: false,
      hasAlert: false
    }));

    loading.value = false;

    console.log('🎯 处理后的运行中任务:', runningTasks.value);

    // 为所有运行中的任务启动WebSocket监控
    if (runningTasks.value.length > 0) {
      console.log('🚀 开始启动WebSocket监控...');
      startWebSocketMonitoring();
    } else {
      console.log('⚠️ 没有运行中的任务，跳过WebSocket监控');
    }
  }).catch(error => {
    console.error('❌ 获取任务列表失败:', error);
    console.error('错误详情:', {
      message: error.message,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data
    });

    loading.value = false;

    // 根据错误类型提供具体的错误提示
    if (error.response?.status === 401) {
      console.error('🔐 认证失败，可能需要重新登录');
      proxy.$modal.msgError('登录已过期，请重新登录');
    } else if (error.response?.status === 403) {
      console.error('🚫 权限不足');
      proxy.$modal.msgError('权限不足，无法访问监控功能');
    } else if (error.response?.status >= 500) {
      console.error('🔧 服务器错误');
      proxy.$modal.msgError('服务器错误，请稍后重试');
    } else {
      proxy.$modal.msgError('获取任务列表失败，请检查网络连接');
    }
  });
}

/** 启动WebSocket监控 */
function startWebSocketMonitoring() {
  console.log('🚀 启动WebSocket监控服务...');
  console.log(`📋 需要监控的任务数量: ${runningTasks.value.length}`);

  // 为所有运行中的任务启动WebSocket监控
  runningTasks.value.forEach((task, index) => {
    console.log(`🔗 启动第${index + 1}个任务的WebSocket: ${task.taskName} (ID: ${task.taskId})`);
    startTaskWebSocket(task);
  });

  console.log('✅ 所有WebSocket监控启动完成');
}

/** 启动任务的WebSocket监控 */
function startTaskWebSocket(task) {
  if (monitorWebSockets.value.has(task.taskId)) {
    return; // 已经有连接了
  }

  try {
    console.log(`🔗 启动WebSocket监控: 任务${task.taskId} - ${task.taskName} (无认证模式)`);

    const wsUrl = `ws://127.0.0.1:9099/api/v1/monitor/stream/${task.taskId}`;
    console.log(`📡 WebSocket URL: ${wsUrl}`);

    const ws = new WebSocket(wsUrl);

    ws.onopen = (event) => {
      console.log(`✅ WebSocket监控连接已建立: ${task.taskName}`);
      console.log(`🔗 连接事件详情:`, event);
      console.log(`📊 WebSocket状态: ${ws.readyState} (0=连接中, 1=已连接, 2=正在关闭, 3=已关闭)`);
      monitorWebSockets.value.set(task.taskId, ws);
    };

    ws.onmessage = (event) => {
      try {
        console.log(`📨 收到原始WebSocket消息:`, {
          data: event.data,
          dataType: typeof event.data,
          dataLength: event.data ? event.data.length : 0,
          timestamp: new Date().toLocaleTimeString()
        });

        const data = JSON.parse(event.data);
        console.log(`📨 解析后的WebSocket消息:`, data.type, data);

        if (data.type === 'connection_success') {
          console.log(`🎉 连接成功确认: ${data.message}`);
          console.log(`🆔 客户端ID: ${data.client_id}`);
        } else if (data.type === 'video_frame' && data.frame) {
          task.currentFrame = 'data:image/jpeg;base64,' + data.frame;
          task.hasDetection = data.has_detection || false;
          const previousAlertState = task.hasAlert;
          task.hasAlert = data.has_alert || false;

          // 检测到新的告警时显示弹窗提示
          if (task.hasAlert && !previousAlertState) {
            showAlertNotification(task, data);
          }

          console.log(`📺 收到视频帧: 检测=${data.has_detection}, 告警=${data.has_alert}, 帧大小=${data.frame.length}字符`);
        } else if (data.type === 'heartbeat') {
          console.log(`💓 收到心跳: ${data.timestamp || '无时间戳'}`);
        } else if (data.type === 'error') {
          console.error(`🚨 收到错误消息:`, {
            code: data.code,
            message: data.message,
            details: data
          });

          // 根据错误代码提供具体的错误说明
          switch (data.code) {
            case 4001:
              console.error(`🔐 认证失败 (4001): 可能是token过期或无效`);
              break;
            case 4002:
              console.error(`📡 推送失败 (4002): 服务器推送数据时出错`);
              break;
            case 4000:
              console.error(`🔧 连接失败 (4000): 服务器内部错误`);
              break;
            default:
              console.error(`❓ 未知错误代码: ${data.code}`);
          }
        } else {
          console.warn(`❓ 收到未知类型的消息:`, data);
        }
      } catch (error) {
        console.error('❌ 解析WebSocket消息失败:', {
          error: error.message,
          rawData: event.data,
          dataType: typeof event.data,
          stack: error.stack
        });
      }
    };

    ws.onerror = (error) => {
      console.error(`❌ WebSocket监控错误 (任务${task.taskId}):`, {
        error: error,
        errorType: error.type,
        target: error.target,
        readyState: ws.readyState,
        url: ws.url,
        timestamp: new Date().toLocaleTimeString()
      });

      // 检查网络连接状态
      if (navigator.onLine) {
        console.log(`🌐 网络连接正常`);
      } else {
        console.error(`🚫 网络连接断开`);
      }

      monitorWebSockets.value.delete(task.taskId);
    };

    ws.onclose = (event) => {
      console.log(`🔌 WebSocket监控连接已关闭: ${task.taskName}`, {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        timestamp: new Date().toLocaleTimeString()
      });

      // 根据关闭代码提供具体说明
      const closeReasons = {
        1000: '正常关闭',
        1001: '端点离开',
        1002: '协议错误',
        1003: '不支持的数据类型',
        1005: '未收到状态码',
        1006: '异常关闭',
        1007: '数据格式错误',
        1008: '策略违规',
        1009: '消息过大',
        1010: '扩展协商失败',
        1011: '服务器错误',
        4000: '服务器内部错误',
        4001: '认证失败',
        4002: '推送失败'
      };

      const reasonText = closeReasons[event.code] || '未知原因';
      console.log(`📋 关闭原因解释: ${reasonText}`);

      if (!event.wasClean) {
        console.warn(`⚠️ 连接非正常关闭，可能是网络问题或服务器错误`);
      }

      monitorWebSockets.value.delete(task.taskId);
    };

  } catch (error) {
    console.error('❌ 启动WebSocket监控失败:', {
      error: error.message,
      stack: error.stack,
      taskId: task.taskId,
      taskName: task.taskName,
      timestamp: new Date().toLocaleTimeString()
    });

    // 检查可能的错误原因
    if (error.name === 'SecurityError') {
      console.error('🔒 安全错误: 可能是HTTPS/WSS协议不匹配或跨域问题');
    } else if (error.name === 'SyntaxError') {
      console.error('📝 语法错误: WebSocket URL格式可能有误');
    } else if (error.name === 'TypeError') {
      console.error('🔧 类型错误: 可能是浏览器不支持WebSocket');
    }
  }
}

/** 停止任务的WebSocket监控 */
function stopTaskWebSocket(task) {
  const ws = monitorWebSockets.value.get(task.taskId);
  if (ws) {
    ws.close();
    monitorWebSockets.value.delete(task.taskId);
    task.currentFrame = null;
    task.hasDetection = false;
    task.hasAlert = false;
  }
}

/** 显示告警通知 */
function showAlertNotification(task, data) {
  try {
    // 获取告警信息
    const alertMessage = data.alert_message || `任务 ${task.taskName} 检测到异常`;
    const alertLevel = data.alert_level || '中';
    const alertTime = new Date().toLocaleString();

    // 根据告警级别确定通知类型
    let notificationType = 'warning';
    let notificationTitle = '告警提示';

    if (alertLevel === '3' || alertLevel === '高') {
      notificationType = 'error';
      notificationTitle = '🚨 高级告警';
    } else if (alertLevel === '1' || alertLevel === '低') {
      notificationType = 'info';
      notificationTitle = '💡 低级告警';
    } else {
      notificationTitle = '⚠️ 告警提示';
    }

    // 构建通知内容
    const notificationContent = `${alertMessage}\n任务: ${task.taskName}\n时间: ${alertTime}`;

    // 显示通知（使用Element Plus的ElNotification直接调用）
    ElNotification({
      title: notificationTitle,
      message: notificationContent,
      type: notificationType,
      duration: 8000, // 8秒后自动关闭
      showClose: true,
      position: 'top-right'
    });

    // 同时显示消息提示
    if (notificationType === 'error') {
      proxy.$modal.msgError(`🚨 ${alertMessage}`);
    } else {
      proxy.$modal.msgWarning(`⚠️ ${alertMessage}`);
    }

    console.log(`🔔 显示告警通知: ${alertMessage} (级别: ${alertLevel})`);

  } catch (error) {
    console.error('❌ 显示告警通知失败:', error);
  }
}

/** 处理图片加载错误 */
function handleImageError(task) {
  task.currentFrame = null;
}

/** 获取视频状态文本 */
function getVideoStatusText(task) {
  if (task.status === '1') {
    return '等待视频流...';
  } else {
    return '任务未运行';
  }
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  switch (status) {
    case '1': return 'success';
    case '0': return 'info';
    case '2': return 'warning';
    default: return 'info';
  }
}

/** 格式化时间 */
function formatTime(time) {
  if (!time) return '未运行';
  return proxy.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 刷新监控 */
function refreshMonitor() {
  getList();
}

// getToken函数已经从@/utils/auth导入，不需要重复定义

/** 跳转到任务管理页面 */
function goToTaskManage() {
  proxy.$router.push('/surveillance/task');
}

/** 从监控列表中移除任务 */
function removeTaskFromList(taskId) {
  const index = runningTasks.value.findIndex(task => task.taskId === taskId);
  if (index !== -1) {
    runningTasks.value.splice(index, 1);
  }
}

/** 停止任务 */
async function stopTask(task) {
  // 防止重复提交
  if (task.stopping) {
    return;
  }

  task.stopping = true;
  try {
    const response = await stopTaskUnified(task.taskId);
    console.log('停止任务响应:', response);

    if (response && response.code === 200) {
      proxy.$modal.msgSuccess(`任务 ${task.taskName} 停止成功`);
      // 停止WebSocket监控
      stopTaskWebSocket(task);
      // 从监控列表中移除该任务
      removeTaskFromList(task.taskId);
    } else if (response && response.success_count !== undefined) {
      proxy.$modal.msgSuccess(`任务 ${task.taskName} 停止成功`);
      // 停止WebSocket监控
      stopTaskWebSocket(task);
      // 从监控列表中移除该任务
      removeTaskFromList(task.taskId);
    } else {
      proxy.$modal.msgError(response?.msg || "任务停止失败");
      getList();
    }

  } catch (error) {
    console.error('停止任务异常:', error);
    if (error.message && error.message.includes('timeout')) {
      proxy.$modal.msgError("请求超时，请稍后重试");
    } else if (error.message && error.message.includes('重复')) {
      proxy.$modal.msgWarning("操作正在处理中，请稍候");
    } else {
      proxy.$modal.msgError(`任务停止异常: ${error.message || error}`);
    }
    getList();
  } finally {
    // 延迟重置状态，避免快速重复点击
    setTimeout(() => {
      task.stopping = false;
    }, 2000);
  }
}












/** 停止全部任务 */
async function stopAllTasks() {
  if (runningTasks.value.length === 0) {
    proxy.$modal.msgWarning('没有可停止的任务');
    return;
  }

  proxy.$modal.confirm(`确认停止 ${runningTasks.value.length} 个任务？`).then(async () => {
    try {
      const taskIds = runningTasks.value.map(task => task.taskId);
      const response = await batchStopTasks(taskIds);

      if (response.code === 200) {
        const result = response.data;
        proxy.$modal.msgSuccess(`批量停止完成：成功 ${result.success_count} 个，失败 ${result.failed_count} 个`);

        // 停止所有WebSocket连接
        runningTasks.value.forEach(task => {
          stopTaskWebSocket(task);
        });

        // 清空监控列表
        runningTasks.value = [];
      } else {
        proxy.$modal.msgError('批量停止失败');
      }
    } catch (error) {
      proxy.$modal.msgError(`批量停止失败: ${error.message}`);
    }
  });
}



// 生命周期
onMounted(() => {
  console.log('🎬 实时监控页面已挂载，开始初始化...');
  getList();
});

onUnmounted(() => {
  // 清理视频帧更新定时器
  if (frameUpdateTimer) {
    clearInterval(frameUpdateTimer);
  }

  // 清理所有WebSocket连接
  monitorWebSockets.value.forEach((ws) => {
    ws.close();
  });
  monitorWebSockets.value.clear();
});
</script>

<style scoped>
.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.monitor-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.monitor-item:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.monitor-item.running {
  border-color: #67c23a;
}

.monitor-item.stopped {
  border-color: #909399;
}

.monitor-item.paused {
  border-color: #e6a23c;
}

.video-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-header {
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-name {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.video-content {
  flex: 1;
  position: relative;
  background: #000;
  min-height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.alert-indicator {
  position: absolute;
  top: 8px;
  right: 100px;
  z-index: 10;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.video-frame {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.no-video {
  text-align: center;
  color: #909399;
}

.no-video p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.video-controls {
  padding: 12px 16px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  text-align: center;
}

.task-info {
  padding: 12px 16px;
  background: #fafafa;
  font-size: 12px;
  color: #606266;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .monitor-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .monitor-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .monitor-item {
    margin: 0 10px;
  }
}


</style>
