2025-07-28 12:18:21.914 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:18:21.914 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:18:21.942 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:18:21.942 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:18:21.943 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:18:21.977 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:18:22.011 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:18:22.011 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:18:23.909 | b4174fafc17342f08db0e284625102fc | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 12:18:23.938 | dbc8f3ed273a48e9856f4ac246bdecca | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 12:18:24.102 | e0e9d655c7474443bde6130b02f84445 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:18:48.290 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:18:48.290 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
