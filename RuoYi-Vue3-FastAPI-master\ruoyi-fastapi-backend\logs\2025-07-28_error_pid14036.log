2025-07-28 09:54:25.503 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 09:54:25.503 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 09:54:25.530 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 09:54:25.530 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 09:54:25.531 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 09:54:25.565 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 09:54:25.598 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 09:54:25.598 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 09:54:32.057 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:54:32.057 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:54:32.057 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:54:32.058 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:54:32.058 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:54:32.058 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:54:32.059 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:54:32.059 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:54:32.059 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:54:32.060 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:54:32.065 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:54:32.065 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:54:32.066 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:54:32.066 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:54:32.068 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:54:32.069 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:54:32.069 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:54:32.069 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:54:32.069 | fa115f1c710c45099258e5e726eb1886 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:54:33.808 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:start_task:202 - 开始启动任务: 12
2025-07-28 09:54:33.810 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:471 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 09:54:33.810 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:478 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 09:54:33.810 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:485 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 09:54:33.810 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:492 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:54:33.811 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:54:33.811 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:499 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 09:54:33.811 | eaa076e0be174f2da4d1b982d7b97b42 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:613 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 09:54:33.811 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:637 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-28 09:54:33.812 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:652 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 09:54:33.812 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:657 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-28 09:54:33.813 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:658 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 09:54:33.813 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:662 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:54:35.116 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:683 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 09:54:35.116 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 09:54:35.116 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 nms_threshold: 0.5
2025-07-28 09:54:35.117 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 input_size: 640
2025-07-28 09:54:35.117 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:451 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:54:35.117 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:695 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:54:37.401 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:707 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 09:54:37.402 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:708 -    - 设备: cuda
2025-07-28 09:54:37.402 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:709 -    - 图像尺寸: 640
2025-07-28 09:54:37.402 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 -    - 置信度阈值: 0.01
2025-07-28 09:54:37.402 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:711 -    - NMS阈值: 0.5
2025-07-28 09:54:37.404 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:724 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 09:54:37.418 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3336 - 任务 12 的监控流已启动
2025-07-28 09:54:37.420 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_cache_task_config:110 - 任务12配置已缓存
2025-07-28 09:54:37.420 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:772 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 09:54:37.421 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:802 - 成功预导入YOLOv5 utils模块
2025-07-28 09:54:37.421 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:808 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:54:37.422 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:824 - 重新加载模块: zql_detect
2025-07-28 09:54:37.422 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:824 - 重新加载模块: model
2025-07-28 09:54:37.422 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:828 - 成功导入智驱力模型
2025-07-28 09:54:37.423 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 09:54:37.423 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 nms_threshold: 0.5
2025-07-28 09:54:37.423 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 input_size: 640
2025-07-28 09:54:37.423 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:451 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:54:37.423 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:840 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:54:37.554 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:853 - 智驱力模型初始化成功
2025-07-28 09:54:37.554 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:854 -    - 设备: cuda
2025-07-28 09:54:37.554 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:855 -    - 图像尺寸: 640
2025-07-28 09:54:37.554 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:856 -    - 置信度阈值: 0.01
2025-07-28 09:54:37.555 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:857 -    - NMS阈值: 0.5
2025-07-28 09:54:37.555 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:detection_loop:870 - 智驱力后处理器初始化成功
2025-07-28 09:55:07.657 | eaa076e0be174f2da4d1b982d7b97b42 | ERROR    | module_stream.service.task_execution_service:detection_loop:890 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-28 09:55:07.659 | eaa076e0be174f2da4d1b982d7b97b42 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:2951 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-28 09:55:07.659 | eaa076e0be174f2da4d1b982d7b97b42 | ERROR    | module_stream.service.task_execution_service:detection_loop:1242 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-28 09:55:07.660 | eaa076e0be174f2da4d1b982d7b97b42 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:2951 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-28 09:55:07.666 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.service.task_execution_service:start_task:253 - 任务 12 启动成功，包括实时监控流
2025-07-28 09:55:07.666 | eaa076e0be174f2da4d1b982d7b97b42 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 09:55:07.671 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:55:07.671 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:55:07.671 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:55:07.671 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:55:07.672 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:55:07.672 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:55:07.672 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:55:07.672 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:55:07.672 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:55:07.677 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:55:07.677 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:55:07.677 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:55:07.677 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:55:07.678 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:55:07.679 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:55:07.679 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:55:07.679 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:55:07.680 | db4e10d9d5ca469cbd3c86068cc3760a | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:55:09.130 | 2a28b8f997da42f38734b6cfd32c227d | ERROR    | exceptions.handle:exception_handler:111 - (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1125, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
    │    │      │       │           │    └ <property object at 0x00000177F2C365C0>
    │    │      │       │           └ <sqlalchemy.engine.base.Connection object at 0x00000177E796B770>
    │    │      │       └ <function DefaultDialect.do_rollback at 0x00000177F2CF6E80>
    │    │      └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x00000177F344A3C0>
    │    └ Engine(mysql+asyncmy://root:***@127.0.0.1:3306/IntelligentSurveillanceAnalyticsSystem)
    └ <sqlalchemy.engine.base.Connection object at 0x00000177E796B770>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 700, in do_rollback
    dbapi_connection.rollback()
    └ <sqlalchemy.pool.base._ConnectionFairy object at 0x00000177C16C97F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 221, in rollback
    self.await_(self._connection.rollback())
    │    │      │    └ <member '_connection' of 'AdaptedConnection' objects>
    │    │      └ <AdaptedConnection <asyncmy.connection.Connection object at 0x000001779ED98830>>
    │    └ <staticmethod(<function await_only at 0x00000177F16623E0>)>
    └ <AdaptedConnection <asyncmy.connection.Connection object at 0x000001779ED98830>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <_cython_3_1_2.coroutine object at 0x000001779EE84B80>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x0000017A0AF7B040 (otid=0x00000177F16199B0) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <_cython_3_1_2.coroutine object at 0x000001779EE84B80>
  File "asyncmy/connection.pyx", line 414, in rollback
    await self._read_ok_packet()
  File "asyncmy/connection.pyx", line 379, in _read_ok_packet
    raise errors.OperationalError(CR_COMMANDS_OUT_OF_SYNC, "Command Out of Sync")
          │      │                └ 2014
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\err...

asyncmy.errors.OperationalError: (2014, 'Command Out of Sync')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 332
               │     └ 3
               └ <function _main at 0x00000177EE2856C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 332
           │    └ <function BaseProcess._bootstrap at 0x00000177EDF94900>
           └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000177EDF93E20>
    └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000177EDF6ECF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
    │    └ <function subprocess_started at 0x00000177F046C040>
    └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001779E7F0440>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x00000177F0467060>
           │       │   └ <uvicorn.server.Server object at 0x000001779E7F0440>
           │       └ <function run at 0x00000177F0110A40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001779EB3B140>
           │      └ <function Runner.run at 0x00000177F0150040>
           └ <asyncio.runners.Runner object at 0x000001779E7F0EC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-pa...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000177F0149A80>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001779E7F0EC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000177F01499E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000177F014B7E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000177EFCD14E0>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001779E7F2BA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001779E...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001779E13FA10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001779E7F2BA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001779E...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001779E...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001779ED982F0>
          └ <fastapi.applications.FastAPI object at 0x000001779E13FA10>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001779EEB54E0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001779ED981A0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001779ED982F0>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\middlewares\trace_middleware\middle.py", line 48, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000177E78F6B60>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x000001779ED98050>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001779ED981A0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x00000177E78F6B60>
          │         │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x000001779ED6B100>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x000001779BDF3E20>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x000001779ED6B100>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001779E7F3E00>
          └ <starlette.middleware.gzip.GZipResponder object at 0x000001779ED6B100>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x000001779ED6B100>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001779E7F3CB0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001779E7F3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x000001779ED6B100>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001779EDA2E00>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001779E7E8D70>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001779E7F3CB0>
          └ <function wrap_app_handling_exceptions at 0x00000177F1446520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000177E78F7C40>
          │   │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001779E7E8D70>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000177E78F7C40>
          │    │                │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001779E7E8D70>>
          └ <fastapi.routing.APIRouter object at 0x000001779E7E8D70>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000177E78F7C40>
          │     │      │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x00000177F1447D80>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000177E78F7C40>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001779E7BF420>
          └ APIRoute(path='/getInfo', name='get_login_user_info', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000177E78F7C40>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001779EDA3460>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000177E78F4720>
          └ <function wrap_app_handling_exceptions at 0x00000177F1446520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000177E78F7880>
          │   │      └ <function RequestResponseCycle.receive at 0x000001779ED8E7A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000177E78F4720>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001779EDA3460>
                     └ <function get_request_handler.<locals>.app at 0x000001779E7BF380>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 290, in app
    async with AsyncExitStack() as async_exit_stack:
               │                   └ <contextlib.AsyncExitStack object at 0x000001779EDA3130>
               └ <class 'contextlib.AsyncExitStack'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\contextlib.py", line 768, in __aexit__
    raise exc
          └ OperationalError("(asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\contextlib.py", line 751, in __aexit__
    cb_suppress = await cb(*exc_details)
                        │   └ (<class 'sqlalchemy.exc.ResourceClosedError'>, ResourceClosedError('This result object does not return rows. It has been clos...
                        └ <bound method _AsyncGeneratorContextManager.__aexit__ of <contextlib._AsyncGeneratorContextManager object at 0x00000177C16CEC...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
          │    │   │      └ ResourceClosedError('This result object does not return rows. It has been closed automatically.')
          │    │   └ <method 'athrow' of 'async_generator' objects>
          │    └ <async_generator object get_db at 0x000001779EF53840>
          └ <contextlib._AsyncGeneratorContextManager object at 0x00000177C16CEC10>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\config\get_db.py", line 11, in get_db
    async with AsyncSessionLocal() as current_db:
               │                      └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000177C17089E0>
               └ async_sessionmaker(class_='AsyncSession', autocommit=False, bind=<sqlalchemy.ext.asyncio.engine.AsyncEngine object at 0x00000...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 1082, in __aexit__
    await asyncio.shield(task)
          │       │      └ <Task finished name='Task-19' coro=<AsyncSession.close() done, defined at C:\Users\<USER>\AppData\Local\Programs\Python\Python3...
          │       └ <function shield at 0x00000177F013E3E0>
          └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 1027, in close
    await greenlet_spawn(self.sync_session.close)
          │              │    │            └ <function Session.close at 0x00000177F3022700>
          │              │    └ <sqlalchemy.orm.session.Session object at 0x00000177C170B680>
          │              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000177C17089E0>
          └ <function greenlet_spawn at 0x00000177F1662520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x0000017A0AF7B040 (otid=0x00000177F16199B0) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2521, in close
    self._close_impl(invalidate=False)
    │    └ <function Session._close_impl at 0x00000177F30228E0>
    └ <sqlalchemy.orm.session.Session object at 0x00000177C170B680>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2590, in _close_impl
    transaction.close(invalidate)
    │           │     └ False
    │           └ <function SessionTransaction.close at 0x00000177F3020EA0>
    └ <sqlalchemy.orm.session.SessionTransaction object at 0x00000177C17CA1D0>
  File "<string>", line 2, in close
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                │  │      │      └ {'invalidate': False}
                │  │      └ ()
                │  └ <sqlalchemy.orm.session.SessionTransaction object at 0x00000177C17CA1D0>
                └ <function SessionTransaction.close at 0x00000177F30209A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 1419, in close
    transaction.close()
    │           └ <function Transaction.close at 0x00000177F2C64FE0>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0AF3CEB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2586, in close
    self._do_close()
    │    └ <function RootTransaction._do_close at 0x00000177F2C65940>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0AF3CEB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2724, in _do_close
    self._close_impl()
    │    └ <function RootTransaction._close_impl at 0x00000177F2C658A0>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0AF3CEB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2710, in _close_impl
    self._connection_rollback_impl()
    │    └ <function RootTransaction._connection_rollback_impl at 0x00000177F2C65760>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0AF3CEB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2702, in _connection_rollback_impl
    self.connection._rollback_impl()
    │    └ <member 'connection' of 'RootTransaction' objects>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0AF3CEB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1127, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
    │    └ <function Connection._handle_dbapi_exception at 0x00000177F2C64860>
    └ <sqlalchemy.engine.base.Connection object at 0x00000177E796B770>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ OperationalError(2014, 'Command Out of Sync')
          │                    │              └ (<class 'asyncmy.errors.OperationalError'>, OperationalError(2014, 'Command Out of Sync'), <traceback object at 0x0000017A0AF...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ OperationalError("(asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1125, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
    │    │      │       │           │    └ <property object at 0x00000177F2C365C0>
    │    │      │       │           └ <sqlalchemy.engine.base.Connection object at 0x00000177E796B770>
    │    │      │       └ <function DefaultDialect.do_rollback at 0x00000177F2CF6E80>
    │    │      └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x00000177F344A3C0>
    │    └ Engine(mysql+asyncmy://root:***@127.0.0.1:3306/IntelligentSurveillanceAnalyticsSystem)
    └ <sqlalchemy.engine.base.Connection object at 0x00000177E796B770>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 700, in do_rollback
    dbapi_connection.rollback()
    └ <sqlalchemy.pool.base._ConnectionFairy object at 0x00000177C16C97F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 221, in rollback
    self.await_(self._connection.rollback())
    │    │      │    └ <member '_connection' of 'AdaptedConnection' objects>
    │    │      └ <AdaptedConnection <asyncmy.connection.Connection object at 0x000001779ED98830>>
    │    └ <staticmethod(<function await_only at 0x00000177F16623E0>)>
    └ <AdaptedConnection <asyncmy.connection.Connection object at 0x000001779ED98830>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <_cython_3_1_2.coroutine object at 0x000001779EE84B80>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x0000017A0AF7B040 (otid=0x00000177F16199B0) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <_cython_3_1_2.coroutine object at 0x000001779EE84B80>
  File "asyncmy/connection.pyx", line 414, in rollback
    await self._read_ok_packet()
  File "asyncmy/connection.pyx", line 379, in _read_ok_packet
    raise errors.OperationalError(CR_COMMANDS_OUT_OF_SYNC, "Command Out of Sync")
          │      │                └ 2014
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\err...

sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 09:55:09.231 | c7f13906e73046808adc9f523d875cc9 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-07-28 09:55:09.267 | 356c841839494334895699ed3694335e | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为5baf2e48-c80c-44cf-9212-93fecd75b334的会话获取图片验证码成功
2025-07-28 09:55:12.671 | 016ed7e1592848babedfc964441a22d7 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-28 09:55:12.691 | db88dafe19064a42ab04bd18a085da52 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 09:55:12.705 | c00b67d89177496e9c66cd56da5000a7 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 09:55:15.456 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:55:15.457 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:55:15.457 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:55:15.457 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:55:15.457 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:55:15.458 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:55:15.458 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:55:15.458 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:55:15.458 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:55:15.461 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:55:15.461 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:55:15.461 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:55:15.462 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:55:15.463 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:55:15.464 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:55:15.464 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:55:15.464 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:55:15.465 | e8ea57f5f5dd48e895d25d315baecd5f | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:55:15.469 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-28 09:55:15.470 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:3393 - 客户端 36ac808f-33a9-4298-a77a-62984c51ca80 已连接到任务 12 的监控流
2025-07-28 09:55:15.470 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 36ac808f-33a9-4298-a77a-62984c51ca80 连接到任务 12 的监控流 (无认证模式)
2025-07-28 09:55:17.538 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_task:279 - 开始停止任务: 12
2025-07-28 09:55:17.540 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_task:303 - 已从运行任务列表移除: 12
2025-07-28 09:55:17.540 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_task:312 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 09:55:17.541 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:3360 - 任务 12 的监控流已停止
2025-07-28 09:55:17.541 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_task:317 - 监控流停止成功: 12
2025-07-28 09:55:17.541 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:131 - 任务12缓存已清除
2025-07-28 09:55:17.541 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_task:326 - 任务缓存清理成功: 12
2025-07-28 09:55:17.550 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_task:335 - 任务状态更新成功: 12
2025-07-28 09:55:17.550 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.service.task_execution_service:stop_task:347 - 任务 12 停止成功，包括实时监控流
2025-07-28 09:55:17.565 | 32a72530303d4f40ab1ca71efc84d4b2 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 09:55:17.605 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:135 - 客户端 36ac808f-33a9-4298-a77a-62984c51ca80 主动断开连接
2025-07-28 09:55:17.606 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:164 - 客户端 36ac808f-33a9-4298-a77a-62984c51ca80 连接已清理
2025-07-28 09:55:19.309 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 09:55:19.310 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:55:19.310 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:55:19.310 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:55:19.310 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:55:19.311 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:55:19.311 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:55:19.311 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 09:55:19.311 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:55:19.311 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:55:19.315 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:55:19.315 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:55:19.316 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:55:19.316 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:55:19.317 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:55:19.318 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:55:19.318 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:55:19.318 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:55:19.318 | 5c2463f6237b4eb48b5e51bad7b19520 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 09:55:20.954 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:start_task:202 - 开始启动任务: 12
2025-07-28 09:55:20.956 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:471 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-28 09:55:20.956 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:478 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-28 09:55:20.956 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:485 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-28 09:55:20.957 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:492 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:55:20.957 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-28 09:55:20.957 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:499 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-28 09:55:20.957 | cbb3458bdf29425ea5f7422eb53915c9 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:613 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-28 09:55:20.958 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:652 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-28 09:55:20.958 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:657 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\model', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master']
2025-07-28 09:55:20.959 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:658 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-28 09:55:20.959 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:662 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:55:20.960 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 重新加载模块: zql_detect
2025-07-28 09:55:20.960 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 重新加载模块: model
2025-07-28 09:55:20.960 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:683 - 验证模型初始化 - 成功导入智驱力模型
2025-07-28 09:55:20.961 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 09:55:20.961 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 nms_threshold: 0.5
2025-07-28 09:55:20.961 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 input_size: 640
2025-07-28 09:55:20.961 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:451 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:55:20.961 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:695 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:55:21.099 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:707 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-28 09:55:21.100 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:708 -    - 设备: cuda
2025-07-28 09:55:21.100 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:709 -    - 图像尺寸: 640
2025-07-28 09:55:21.100 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 -    - 置信度阈值: 0.01
2025-07-28 09:55:21.100 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:711 -    - NMS阈值: 0.5
2025-07-28 09:55:21.100 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:724 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-28 09:55:21.116 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:3336 - 任务 12 的监控流已启动
2025-07-28 09:55:21.118 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_cache_task_config:110 - 任务12配置已缓存
2025-07-28 09:55:21.118 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:772 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-28 09:55:21.119 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:802 - 成功预导入YOLOv5 utils模块
2025-07-28 09:55:21.119 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:808 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-28 09:55:21.119 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:824 - 重新加载模块: zql_detect
2025-07-28 09:55:21.121 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:824 - 重新加载模块: model
2025-07-28 09:55:21.121 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:828 - 成功导入智驱力模型
2025-07-28 09:55:21.121 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 confidence_threshold: 0.01
2025-07-28 09:55:21.121 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 nms_threshold: 0.5
2025-07-28 09:55:21.121 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:564 - 从model_params获取 input_size: 640
2025-07-28 09:55:21.121 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:451 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:55:21.122 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:840 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-28 09:55:21.243 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:853 - 智驱力模型初始化成功
2025-07-28 09:55:21.244 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:854 -    - 设备: cuda
2025-07-28 09:55:21.244 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:855 -    - 图像尺寸: 640
2025-07-28 09:55:21.244 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:856 -    - 置信度阈值: 0.01
2025-07-28 09:55:21.244 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:857 -    - NMS阈值: 0.5
2025-07-28 09:55:21.244 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:detection_loop:870 - 智驱力后处理器初始化成功
2025-07-28 09:55:51.283 | cbb3458bdf29425ea5f7422eb53915c9 | ERROR    | module_stream.service.task_execution_service:detection_loop:890 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-28 09:55:51.285 | cbb3458bdf29425ea5f7422eb53915c9 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:2951 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-28 09:55:51.285 | cbb3458bdf29425ea5f7422eb53915c9 | ERROR    | module_stream.service.task_execution_service:detection_loop:1242 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-28 09:55:51.286 | cbb3458bdf29425ea5f7422eb53915c9 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:2951 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-28 09:55:51.294 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 09:55:51.295 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 09:55:51.295 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:55:51.295 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 09:55:51.295 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 09:55:51.295 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 09:55:51.295 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 09:55:51.296 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 09:55:51.296 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 09:55:51.297 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.service.task_execution_service:start_task:253 - 任务 12 启动成功，包括实时监控流
2025-07-28 09:55:51.298 | cbb3458bdf29425ea5f7422eb53915c9 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-28 09:55:51.300 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 09:55:51.300 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 09:55:51.300 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 09:55:51.300 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 09:55:51.302 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 09:55:51.303 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 09:55:51.304 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 09:55:51.304 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 09:55:51.304 | 9bfb45648e6946e8b81424ea31660041 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 09:55:55.323 | 6f7d29dd9ca84926a151233ffd1b1345 | ERROR    | exceptions.handle:exception_handler:111 - (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1125, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
    │    │      │       │           │    └ <property object at 0x00000177F2C365C0>
    │    │      │       │           └ <sqlalchemy.engine.base.Connection object at 0x00000177E79698D0>
    │    │      │       └ <function DefaultDialect.do_rollback at 0x00000177F2CF6E80>
    │    │      └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x00000177F344A3C0>
    │    └ Engine(mysql+asyncmy://root:***@127.0.0.1:3306/IntelligentSurveillanceAnalyticsSystem)
    └ <sqlalchemy.engine.base.Connection object at 0x00000177E79698D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 700, in do_rollback
    dbapi_connection.rollback()
    └ <sqlalchemy.pool.base._ConnectionFairy object at 0x0000017A0D42D3D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 221, in rollback
    self.await_(self._connection.rollback())
    │    │      │    └ <member '_connection' of 'AdaptedConnection' objects>
    │    │      └ <AdaptedConnection <asyncmy.connection.Connection object at 0x0000017A0AF3B390>>
    │    └ <staticmethod(<function await_only at 0x00000177F16623E0>)>
    └ <AdaptedConnection <asyncmy.connection.Connection object at 0x0000017A0AF3B390>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <_cython_3_1_2.coroutine object at 0x0000017A0D407EB0>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x0000017A0D4626C0 (otid=0x00000177F16199B0) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <_cython_3_1_2.coroutine object at 0x0000017A0D407EB0>
  File "asyncmy/connection.pyx", line 414, in rollback
    await self._read_ok_packet()
  File "asyncmy/connection.pyx", line 379, in _read_ok_packet
    raise errors.OperationalError(CR_COMMANDS_OUT_OF_SYNC, "Command Out of Sync")
          │      │                └ 2014
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\err...

asyncmy.errors.OperationalError: (2014, 'Command Out of Sync')


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 332
               │     └ 3
               └ <function _main at 0x00000177EE2856C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 332
           │    └ <function BaseProcess._bootstrap at 0x00000177EDF94900>
           └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x00000177EDF93E20>
    └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x00000177EDF6ECF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
    │    └ <function subprocess_started at 0x00000177F046C040>
    └ <SpawnProcess name='SpawnProcess-1' parent=33140 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001779E7F0440>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x00000177F0467060>
           │       │   └ <uvicorn.server.Server object at 0x000001779E7F0440>
           │       └ <function run at 0x00000177F0110A40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001779EB3B140>
           │      └ <function Runner.run at 0x00000177F0150040>
           └ <asyncio.runners.Runner object at 0x000001779E7F0EC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-pa...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000177F0149A80>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001779E7F0EC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x00000177F01499E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000177F014B7E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000177EFCD14E0>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001779E7F2BA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017A0D...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001779E13FA10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001779E7F2BA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017A0D...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017A0D...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001779ED982F0>
          └ <fastapi.applications.FastAPI object at 0x000001779E13FA10>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000017A0D3FBB00>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000017...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001779ED981A0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001779ED982F0>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\middlewares\trace_middleware\middle.py", line 48, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000017A0D3FB920>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x000001779ED98050>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001779ED981A0>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000017A0D3FB920>
          │         │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000017A0AFB6B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x000001779BDF3E20>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000017A0AFB6B60>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001779E7F3E00>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000017A0AFB6B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000017A0AFB6B60>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001779E7F3CB0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001779E7F3E00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000017A0AFB6B60>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000177C1800F70>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001779E7E8D70>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001779E7F3CB0>
          └ <function wrap_app_handling_exceptions at 0x00000177F1446520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001779EF8B920>
          │   │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001779E7E8D70>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001779EF8B920>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001779E7E8D70>>
          └ <fastapi.routing.APIRouter object at 0x000001779E7E8D70>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001779EF8B920>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x00000177F1447D80>
          └ APIRoute(path='/stream_manage/task/list', name='get_task_list', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001779EF8B920>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001779EADB420>
          └ APIRoute(path='/stream_manage/task/list', name='get_task_list', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001779EF8B920>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000177C18020A0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001779EF88EA0>
          └ <function wrap_app_handling_exceptions at 0x00000177F1446520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000177E78F4900>
          │   │      └ <function RequestResponseCycle.receive at 0x0000017A0D3FBBA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001779EF88EA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000177C18020A0>
                     └ <function get_request_handler.<locals>.app at 0x000001779EADB2E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 290, in app
    async with AsyncExitStack() as async_exit_stack:
               │                   └ <contextlib.AsyncExitStack object at 0x00000177C155A670>
               └ <class 'contextlib.AsyncExitStack'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\contextlib.py", line 768, in __aexit__
    raise exc
          └ OperationalError("(asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\contextlib.py", line 751, in __aexit__
    cb_suppress = await cb(*exc_details)
                        │   └ (<class 'sqlalchemy.exc.ResourceClosedError'>, ResourceClosedError('This result object does not return rows. It has been clos...
                        └ <bound method _AsyncGeneratorContextManager.__aexit__ of <contextlib._AsyncGeneratorContextManager object at 0x0000017A0B0114...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\contextlib.py", line 235, in __aexit__
    await self.gen.athrow(value)
          │    │   │      └ ResourceClosedError('This result object does not return rows. It has been closed automatically.')
          │    │   └ <method 'athrow' of 'async_generator' objects>
          │    └ <async_generator object get_db at 0x000001779C035B60>
          └ <contextlib._AsyncGeneratorContextManager object at 0x0000017A0B0114E0>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\config\get_db.py", line 11, in get_db
    async with AsyncSessionLocal() as current_db:
               │                      └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000177C16A1850>
               └ async_sessionmaker(class_='AsyncSession', autocommit=False, bind=<sqlalchemy.ext.asyncio.engine.AsyncEngine object at 0x00000...

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 1082, in __aexit__
    await asyncio.shield(task)
          │       │      └ <Task finished name='Task-57' coro=<AsyncSession.close() done, defined at C:\Users\<USER>\AppData\Local\Programs\Python\Python3...
          │       └ <function shield at 0x00000177F013E3E0>
          └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 1027, in close
    await greenlet_spawn(self.sync_session.close)
          │              │    │            └ <function Session.close at 0x00000177F3022700>
          │              │    └ <sqlalchemy.orm.session.Session object at 0x00000177C16E8050>
          │              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x00000177C16A1850>
          └ <function greenlet_spawn at 0x00000177F1662520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x0000017A0D4626C0 (otid=0x00000177F16199B0) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2521, in close
    self._close_impl(invalidate=False)
    │    └ <function Session._close_impl at 0x00000177F30228E0>
    └ <sqlalchemy.orm.session.Session object at 0x00000177C16E8050>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 2590, in _close_impl
    transaction.close(invalidate)
    │           │     └ False
    │           └ <function SessionTransaction.close at 0x00000177F3020EA0>
    └ <sqlalchemy.orm.session.SessionTransaction object at 0x0000017A0D461210>
  File "<string>", line 2, in close
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                │  │      │      └ {'invalidate': False}
                │  │      └ ()
                │  └ <sqlalchemy.orm.session.SessionTransaction object at 0x0000017A0D461210>
                └ <function SessionTransaction.close at 0x00000177F30209A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 1419, in close
    transaction.close()
    │           └ <function Transaction.close at 0x00000177F2C64FE0>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0D48FB10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2586, in close
    self._do_close()
    │    └ <function RootTransaction._do_close at 0x00000177F2C65940>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0D48FB10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2724, in _do_close
    self._close_impl()
    │    └ <function RootTransaction._close_impl at 0x00000177F2C658A0>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0D48FB10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2710, in _close_impl
    self._connection_rollback_impl()
    │    └ <function RootTransaction._connection_rollback_impl at 0x00000177F2C65760>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0D48FB10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2702, in _connection_rollback_impl
    self.connection._rollback_impl()
    │    └ <member 'connection' of 'RootTransaction' objects>
    └ <sqlalchemy.engine.base.RootTransaction object at 0x0000017A0D48FB10>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1127, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
    │    └ <function Connection._handle_dbapi_exception at 0x00000177F2C64860>
    └ <sqlalchemy.engine.base.Connection object at 0x00000177E79698D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ OperationalError(2014, 'Command Out of Sync')
          │                    │              └ (<class 'asyncmy.errors.OperationalError'>, OperationalError(2014, 'Command Out of Sync'), <traceback object at 0x0000017A0D4...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ OperationalError("(asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1125, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
    │    │      │       │           │    └ <property object at 0x00000177F2C365C0>
    │    │      │       │           └ <sqlalchemy.engine.base.Connection object at 0x00000177E79698D0>
    │    │      │       └ <function DefaultDialect.do_rollback at 0x00000177F2CF6E80>
    │    │      └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x00000177F344A3C0>
    │    └ Engine(mysql+asyncmy://root:***@127.0.0.1:3306/IntelligentSurveillanceAnalyticsSystem)
    └ <sqlalchemy.engine.base.Connection object at 0x00000177E79698D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 700, in do_rollback
    dbapi_connection.rollback()
    └ <sqlalchemy.pool.base._ConnectionFairy object at 0x0000017A0D42D3D0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 221, in rollback
    self.await_(self._connection.rollback())
    │    │      │    └ <member '_connection' of 'AdaptedConnection' objects>
    │    │      └ <AdaptedConnection <asyncmy.connection.Connection object at 0x0000017A0AF3B390>>
    │    └ <staticmethod(<function await_only at 0x00000177F16623E0>)>
    └ <AdaptedConnection <asyncmy.connection.Connection object at 0x0000017A0AF3B390>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <_cython_3_1_2.coroutine object at 0x0000017A0D407EB0>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x0000017A0D4626C0 (otid=0x00000177F16199B0) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <_cython_3_1_2.coroutine object at 0x0000017A0D407EB0>
  File "asyncmy/connection.pyx", line 414, in rollback
    await self._read_ok_packet()
  File "asyncmy/connection.pyx", line 379, in _read_ok_packet
    raise errors.OperationalError(CR_COMMANDS_OUT_OF_SYNC, "Command Out of Sync")
          │      │                └ 2014
          │      └ <class 'asyncmy.errors.OperationalError'>
          └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\err...

sqlalchemy.exc.OperationalError: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-28 09:56:35.412 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 09:56:35.413 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
