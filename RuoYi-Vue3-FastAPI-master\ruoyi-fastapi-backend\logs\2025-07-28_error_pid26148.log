2025-07-28 10:29:21.175 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 10:29:21.175 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 10:29:21.201 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 10:29:21.202 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 10:29:21.203 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 10:29:21.238 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 10:29:21.253 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 10:29:21.253 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 10:29:36.478 | 9a093f56fca341819c86c2199bb55869 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:29:36.494 | 9bb2148a59bb47c69f5965e177adf702 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:29:36.676 | f6961e6ff00c4881b91deac18deccd16 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:30:04.965 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 10:30:04.965 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
