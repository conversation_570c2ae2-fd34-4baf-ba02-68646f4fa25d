2025-07-28 13:10:19.053 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 13:10:19.053 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 13:10:19.082 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 13:10:19.082 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 13:10:19.084 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 13:10:19.115 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 13:10:19.145 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 13:10:19.145 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 13:10:24.428 | d7acaaf22b794e03a6bde07d912b94c1 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为b39c787e-0c12-460b-b3ef-57e33201bdff的会话获取图片验证码成功
2025-07-28 13:10:29.376 | 1b0566f9769149a9ae8e0d0e3cb26d5c | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-28 13:10:29.420 | 485283640b544496b98cf905d7df4591 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 13:10:29.439 | 2686438567354affa9149a625d91518a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 13:10:33.573 | 82f19420cc3147e4bd8bce933e5048a2 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 13:10:33.573 | 82f19420cc3147e4bd8bce933e5048a2 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 13:10:33.578 | 82f19420cc3147e4bd8bce933e5048a2 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 13:10:34.838 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 13:10:34.838 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 13:10:34.838 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 13:10:34.838 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 13:10:34.839 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 13:10:34.839 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 13:10:34.839 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 13:10:34.839 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 13:10:34.840 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 13:10:34.840 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 13:10:34.852 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 13:10:34.852 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 13:10:34.852 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 13:10:34.853 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 13:10:34.855 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 13:10:34.856 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 13:10:34.856 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 13:10:34.856 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 13:10:34.857 | 381e065eaaed470c8c40afbd5f8858a1 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 13:10:35.997 | 1107fa90a2a44ca08e1c6a45f5dd4545 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 13:14:18.178 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 13:14:18.178 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
