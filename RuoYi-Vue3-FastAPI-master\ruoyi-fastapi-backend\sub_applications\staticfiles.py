import os
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from config.env import UploadConfig


def mount_staticfiles(app: FastAPI):
    """
    挂载静态文件
    """
    # 挂载原有的上传文件目录
    app.mount(f'{UploadConfig.UPLOAD_PREFIX}', StaticFiles(directory=f'{UploadConfig.UPLOAD_PATH}'), name='profile')

    # 挂载静态文件目录（用于告警截图等）
    static_dir = "static"
    if not os.path.exists(static_dir):
        os.makedirs(static_dir, exist_ok=True)
    app.mount('/static', StaticFiles(directory=static_dir), name='static')
