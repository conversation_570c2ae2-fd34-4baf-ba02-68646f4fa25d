2025-07-28 14:05:06.595 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 14:05:06.596 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 14:05:06.622 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 14:05:06.622 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 14:05:06.623 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 14:05:06.657 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 14:05:06.683 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 14:05:06.683 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 14:06:14.225 | a45c19e5e9844f7187d71480471e5122 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 14:06:14.239 | 95aac3bfe138483c8b3d7eb6b57d0d38 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 14:06:14.392 | de6106590d0343bbbbda8b0e52346e8e | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 14:06:18.081 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1060: 实体对象 = True
2025-07-28 14:06:18.082 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140329_902.jpg
2025-07-28 14:06:18.082 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140329_902.jpg
2025-07-28 14:06:18.084 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1060
2025-07-28 14:06:18.085 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1059: 实体对象 = True
2025-07-28 14:06:18.085 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140327_666.jpg
2025-07-28 14:06:18.085 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140327_666.jpg
2025-07-28 14:06:18.087 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1059
2025-07-28 14:06:18.089 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1058: 实体对象 = True
2025-07-28 14:06:18.089 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140325_429.jpg
2025-07-28 14:06:18.089 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140325_429.jpg
2025-07-28 14:06:18.090 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1058
2025-07-28 14:06:18.091 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1057: 实体对象 = True
2025-07-28 14:06:18.092 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140323_200.jpg
2025-07-28 14:06:18.092 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140323_200.jpg
2025-07-28 14:06:18.093 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1057
2025-07-28 14:06:18.095 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1056: 实体对象 = True
2025-07-28 14:06:18.095 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140320_986.jpg
2025-07-28 14:06:18.096 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140320_986.jpg
2025-07-28 14:06:18.097 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1056
2025-07-28 14:06:18.098 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1055: 实体对象 = True
2025-07-28 14:06:18.098 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140318_788.jpg
2025-07-28 14:06:18.098 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140318_788.jpg
2025-07-28 14:06:18.099 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1055
2025-07-28 14:06:18.101 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1054: 实体对象 = True
2025-07-28 14:06:18.101 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140316_595.jpg
2025-07-28 14:06:18.101 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140316_595.jpg
2025-07-28 14:06:18.102 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1054
2025-07-28 14:06:18.103 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1053: 实体对象 = True
2025-07-28 14:06:18.103 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:_delete_alert_screenshot:270 - 截图文件不存在或不是文件: uploads\static\alert_screenshots\alert_12_8_20250728_140316_341.jpg
2025-07-28 14:06:18.104 | 38a92d76f7b14ce58814883edf91b4ad | WARNING  | module_alert.service.alert_service:delete_alert_services:185 - 删除告警截图失败: static\alert_screenshots\alert_12_8_20250728_140316_341.jpg
2025-07-28 14:06:18.105 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1053
2025-07-28 14:06:18.108 | 38a92d76f7b14ce58814883edf91b4ad | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除8条记录
2025-07-28 14:06:18.143 | 97f2ad02220e47b096f3a525983af450 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 14:06:20.691 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 14:06:20.691 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 14:06:20.691 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 14:06:20.691 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 14:06:20.692 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 14:06:20.693 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 14:06:20.694 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-28 14:06:20.694 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 14:06:20.694 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 14:06:20.700 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 14:06:20.700 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 14:06:20.700 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 14:06:20.700 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 14:06:20.702 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 14:06:20.702 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 14:06:20.703 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 14:06:20.703 | ced69065dfaf4ac6b92ab9e4e5000bb8 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-28 14:06:22.303 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 14:06:22.308 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 14:06:22.309 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 14:06:22.309 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 14:06:22.309 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 14:06:22.309 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 14:06:22.310 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 14:06:22.310 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 14:06:22.311 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 14:06:22.311 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 14:06:22.317 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 14:06:22.317 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 14:06:22.317 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 14:06:22.317 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 14:06:22.318 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 14:06:22.319 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 14:06:22.319 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 14:06:22.319 | 2d85995cc37445a382c26c1c0b6bceaf | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 14:07:00.394 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 14:07:00.395 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
