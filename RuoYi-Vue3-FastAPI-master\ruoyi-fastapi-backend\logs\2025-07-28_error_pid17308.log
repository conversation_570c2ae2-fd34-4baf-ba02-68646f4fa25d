2025-07-28 13:15:03.972 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 13:15:03.973 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 13:15:04.001 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 13:15:04.001 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 13:15:04.002 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 13:15:04.037 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 13:15:04.070 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 13:15:04.070 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 13:15:18.335 | 3e7948e9bb1d4b5eaca00d6aa78b3dcb | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 13:15:28.592 | 8bf8524ea2d84538886f4bf5f0c55f8c | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 13:17:12.436 | 36b8f19c9ca540eebf5a60eb8868c224 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 13:18:53.397 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 13:18:53.397 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
