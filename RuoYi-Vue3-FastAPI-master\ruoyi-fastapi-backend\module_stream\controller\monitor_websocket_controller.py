"""
实时监控WebSocket控制器
负责处理实时视频流的WebSocket连接和推送
"""

import asyncio
import json
import uuid
from typing import Dict, Set
import base64

from fastapi import WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_stream.service.task_execution_service import TaskExecutionService
from module_admin.service.login_service import LoginService
from utils.log_util import logger
import jwt
from config.env import JwtConfig
from module_admin.dao.user_dao import UserDao
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from utils.common_util import CamelCaseUtil


class MonitorWebSocketController:
    """
    实时监控WebSocket控制器
    """
    
    # 活跃的WebSocket连接 {client_id: {'websocket': ws, 'task_id': int, 'user_id': int}}
    active_connections: Dict[str, Dict] = {}
    
    # 任务订阅者 {task_id: {client_id1, client_id2, ...}}
    task_subscribers: Dict[int, Set[str]] = {}

    @classmethod
    async def connect_monitor_stream(
        cls,
        websocket: WebSocket,
        task_id: int
    ):
        """
        连接实时监控流（无认证版本）

        :param websocket: WebSocket连接
        :param task_id: 任务ID
        """
        client_id = str(uuid.uuid4())
        
        try:
            logger.info(f"开始WebSocket连接: 任务{task_id} (无认证模式)")

            # 直接接受WebSocket连接，无需认证
            await websocket.accept()
            
            # 注册连接
            cls.active_connections[client_id] = {
                'websocket': websocket,
                'task_id': task_id,
                'user_id': 'anonymous',  # 无认证模式，使用匿名用户
                'connected_at': asyncio.get_event_loop().time()
            }
            
            # 添加到任务订阅者
            if task_id not in cls.task_subscribers:
                cls.task_subscribers[task_id] = set()
            cls.task_subscribers[task_id].add(client_id)

            # 通知任务执行服务添加监控客户端
            TaskExecutionService.add_monitor_client(task_id, client_id)

            logger.info(f"客户端 {client_id} 连接到任务 {task_id} 的监控流 (无认证模式)")

            # 发送连接成功消息
            await websocket.send_json({
                'type': 'connection_success',
                'client_id': client_id,
                'task_id': task_id,
                'message': '连接成功 (无认证模式)'
            })

            # 开始推送监控流
            await cls._start_stream_push(client_id, task_id, websocket)
            
        except WebSocketDisconnect:
            logger.info(f"客户端 {client_id} 断开连接")
        except Exception as e:
            logger.error(f"监控流连接失败: {e}")
            try:
                await websocket.close(code=4000, reason=f"连接失败: {str(e)}")
            except:
                pass
        finally:
            # 清理连接
            await cls._cleanup_connection(client_id, task_id)

    @classmethod
    async def _start_stream_push(cls, client_id: str, task_id: int, websocket: WebSocket):
        """
        开始推送监控流
        """
        try:
            while client_id in cls.active_connections:
                # 获取监控帧
                frame_data = TaskExecutionService.get_monitor_frame(task_id, timeout=1.0)
                
                if frame_data:
                    # 编码为base64
                    frame_base64 = base64.b64encode(frame_data['frame_data']).decode('utf-8')
                    
                    # 发送帧数据
                    message = {
                        'type': 'video_frame',
                        'task_id': task_id,
                        'frame': frame_base64,
                        'timestamp': frame_data['timestamp'],
                        'has_detection': frame_data['has_detection'],
                        'has_alert': frame_data['has_alert']
                    }

                    # 如果有告警，添加告警详细信息
                    if frame_data['has_alert']:
                        message['alert_message'] = frame_data.get('alert_message', '检测到异常')
                        message['alert_level'] = frame_data.get('alert_level', '2')
                        message['alert_type'] = frame_data.get('alert_type', 'unknown')
                        message['alert_details'] = frame_data.get('alert_details', {})
                    
                    await websocket.send_json(message)
                else:
                    # 没有新帧，发送心跳
                    await websocket.send_json({
                        'type': 'heartbeat',
                        'task_id': task_id,
                        'timestamp': asyncio.get_event_loop().time()
                    })
                
                # 优化推送频率：减少延迟，提高流畅度
                await asyncio.sleep(0.033)  # 约30FPS，参考yolo_ROI_ai.py的实时性
                
        except WebSocketDisconnect:
            logger.info(f"客户端 {client_id} 主动断开连接")
        except Exception as e:
            logger.error(f"推送监控流失败: {e}")
            try:
                await websocket.close(code=4002, reason=f"推送失败: {str(e)}")
            except:
                pass

    @classmethod
    async def _cleanup_connection(cls, client_id: str, task_id: int):
        """
        清理连接
        """
        try:
            # 从活跃连接中移除
            if client_id in cls.active_connections:
                del cls.active_connections[client_id]
            
            # 从任务订阅者中移除
            if task_id in cls.task_subscribers:
                cls.task_subscribers[task_id].discard(client_id)
                
                # 如果没有订阅者了，清理任务订阅
                if not cls.task_subscribers[task_id]:
                    del cls.task_subscribers[task_id]
            
            # 通知任务执行服务移除监控客户端
            TaskExecutionService.remove_monitor_client(task_id, client_id)
            
            logger.info(f"客户端 {client_id} 连接已清理")
            
        except Exception as e:
            logger.error(f"清理连接失败: {e}")

    @classmethod
    async def broadcast_to_task(cls, task_id: int, message: Dict):
        """
        向任务的所有订阅者广播消息
        """
        try:
            if task_id not in cls.task_subscribers:
                return
            
            # 获取任务的所有订阅者
            subscribers = cls.task_subscribers[task_id].copy()
            
            # 并发发送消息
            tasks = []
            for client_id in subscribers:
                if client_id in cls.active_connections:
                    websocket = cls.active_connections[client_id]['websocket']
                    tasks.append(cls._send_message_safe(websocket, message, client_id))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        except Exception as e:
            logger.error(f"广播消息失败: {e}")

    @classmethod
    async def _send_message_safe(cls, websocket: WebSocket, message: Dict, client_id: str):
        """
        安全发送消息
        """
        try:
            await websocket.send_json(message)
        except Exception as e:
            logger.warning(f"向客户端 {client_id} 发送消息失败: {e}")
            # 标记连接为无效，将在下次清理时移除

    @classmethod
    def get_connection_stats(cls) -> Dict:
        """
        获取连接统计信息
        """
        try:
            stats = {
                'total_connections': len(cls.active_connections),
                'task_subscribers': {},
                'connections_by_task': {}
            }
            
            # 统计每个任务的订阅者数量
            for task_id, subscribers in cls.task_subscribers.items():
                stats['task_subscribers'][task_id] = len(subscribers)
            
            # 统计每个任务的连接详情
            for client_id, conn_info in cls.active_connections.items():
                task_id = conn_info['task_id']
                if task_id not in stats['connections_by_task']:
                    stats['connections_by_task'][task_id] = []
                
                stats['connections_by_task'][task_id].append({
                    'client_id': client_id,
                    'user_id': conn_info['user_id'],
                    'connected_at': conn_info['connected_at']
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"获取连接统计失败: {e}")
            return {'error': str(e)}

    @classmethod
    async def disconnect_all_clients(cls):
        """
        断开所有客户端连接
        """
        try:
            # 获取所有连接的副本
            connections = list(cls.active_connections.items())
            
            # 并发断开所有连接
            tasks = []
            for client_id, conn_info in connections:
                websocket = conn_info['websocket']
                tasks.append(cls._disconnect_client_safe(websocket, client_id))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            # 清理所有数据结构
            cls.active_connections.clear()
            cls.task_subscribers.clear()
            
            logger.info("所有监控客户端已断开连接")
            
        except Exception as e:
            logger.error(f"断开所有客户端失败: {e}")

    @classmethod
    async def _disconnect_client_safe(cls, websocket: WebSocket, client_id: str):
        """
        安全断开客户端连接
        """
        try:
            await websocket.close(code=4003, reason="服务器关闭")
        except Exception as e:
            logger.warning(f"断开客户端 {client_id} 失败: {e}")

    @classmethod
    async def _verify_websocket_token(cls, token: str, query_db: AsyncSession):
        """
        验证WebSocket token（简化版本，只验证JWT有效性）
        """
        try:
            # 记录原始token信息用于调试
            logger.info(f"WebSocket token验证开始: 原始token长度={len(token) if token else 0}")
            logger.info(f"WebSocket token前20字符: {token[:20] if token else 'None'}...")

            # 解码token - 处理不同的token格式
            if token.startswith('Bearer '):
                token = token[7:]
                logger.info("WebSocket token: 移除Bearer前缀")

            # 记录处理后的token信息
            logger.info(f"WebSocket token验证: 处理后token长度={len(token) if token else 0}")

            if not token:
                logger.warning('WebSocket token为空')
                return None

            # 尝试解码JWT token
            logger.info("WebSocket token: 开始解码JWT")
            logger.info(f"WebSocket token: 使用的JWT密钥前10位: {JwtConfig.jwt_secret_key[:10]}...")
            logger.info(f"WebSocket token: 使用的JWT算法: {JwtConfig.jwt_algorithm}")

            # 尝试解码JWT，但不强制验证过期时间（用于调试）
            try:
                payload = jwt.decode(token, JwtConfig.jwt_secret_key, algorithms=[JwtConfig.jwt_algorithm])
                logger.info(f"WebSocket token: JWT解码成功，payload={payload}")
            except jwt.ExpiredSignatureError:
                logger.warning("WebSocket token: JWT已过期，但继续验证用于调试")
                # 解码但不验证过期时间
                payload = jwt.decode(token, JwtConfig.jwt_secret_key, algorithms=[JwtConfig.jwt_algorithm], options={"verify_exp": False})
                logger.info(f"WebSocket token: JWT解码成功（忽略过期），payload={payload}")
            except Exception as e:
                logger.error(f"WebSocket token: JWT解码失败: {e}")
                raise

            user_id = payload.get('user_id')
            session_id = payload.get('session_id')

            if not user_id:
                logger.warning('WebSocket token中没有用户ID')
                return None

            logger.info(f"WebSocket token解析成功: user_id={user_id}, session_id={session_id}")

            # 获取用户信息
            logger.info(f"WebSocket token: 开始查询用户信息 user_id={user_id}")
            query_user = await UserDao.get_user_by_id(query_db, user_id=int(user_id))
            logger.info(f"WebSocket token: 用户查询结果 query_user={query_user}")

            if not query_user.get('user_basic_info'):
                logger.warning(f'WebSocket token对应的用户不存在或状态异常: user_id={user_id}')
                logger.warning(f'用户查询结果详情: {query_user}')
                return None

            # WebSocket连接简化认证：只验证JWT token有效性，不强制要求Redis验证
            # 这样可以避免Redis连接问题，同时保持基本的安全性
            logger.info(f"WebSocket token验证通过: user_id={user_id}, session_id={session_id}")

            # 构建用户对象
            current_user = CurrentUserModel(
                permissions=[],
                roles=[],
                user=UserInfoModel(
                    **CamelCaseUtil.transform_result(query_user.get('user_basic_info')),
                    postIds="",  # 字符串类型，不是列表
                    roleIds="",  # 字符串类型，不是列表
                    dept=CamelCaseUtil.transform_result(query_user.get('user_dept_info')),
                    role=CamelCaseUtil.transform_result(query_user.get('user_role_info')),
                ),
            )

            logger.info(f"WebSocket用户验证成功: {current_user.user.userName}")
            return current_user

        except jwt.ExpiredSignatureError as e:
            logger.warning(f'WebSocket token已过期: {e}')
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f'WebSocket token无效: {e}')
            return None
        except Exception as e:
            logger.error(f"WebSocket token验证失败: {e}")
            import traceback
            logger.error(f"WebSocket token验证异常堆栈: {traceback.format_exc()}")
            return None


# 创建全局实例
monitor_websocket_controller = MonitorWebSocketController()
