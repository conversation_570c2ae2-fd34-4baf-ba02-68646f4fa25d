2025-07-28 10:40:44.487 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 10:40:44.488 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 10:40:44.513 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 10:40:44.513 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 10:40:44.514 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 10:40:44.542 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 10:40:44.551 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 10:40:44.551 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 10:42:54.374 | 6837ddc6260e4cce90b027301a712f98 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:42:54.389 | 9ecec9f9ba174503b848d0d79460749b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:42:54.531 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 10:42:54.531 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 10:42:54.532 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 10:42:54.532 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 10:42:54.532 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 10:42:54.533 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 10:42:54.533 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 10:42:54.533 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 10:42:54.533 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 10:42:54.534 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 10:42:54.543 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 10:42:54.543 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 10:42:54.544 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 10:42:54.544 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 10:42:54.547 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 10:42:54.548 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 10:42:54.549 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 10:42:54.549 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 10:42:54.549 | 0e1d65f651954211adac4d1f44f09f03 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 10:42:57.334 | 55e9c13510bd4369b7fc87ad0cec4cec | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:43:04.637 | 94449cfb59e646e1bf1d1e5612d5446f | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:43:10.821 | 19665c574bd244fb8dfe602fcf491845 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:43:12.416 | 4fbc01f83ab043b69d510e8a2ccf64c7 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:43:13.705 | d09a6991baec453b9753c95352cceaf0 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 10:43:21.130 | a16cff6a41e348b380a2bdb5d24e13d1 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 10:43:21.130 | a16cff6a41e348b380a2bdb5d24e13d1 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 10:43:21.134 | a16cff6a41e348b380a2bdb5d24e13d1 | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 10:43:23.128 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:list_available_algorithms:792 - 找到 3 个可用算法
2025-07-28 10:43:23.130 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: car_counting
2025-07-28 10:43:23.130 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 10:43:23.130 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 10:43:23.131 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 10:43:23.140 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: person_counting
2025-07-28 10:43:23.141 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 10:43:23.141 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 10:43:23.141 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 10:43:23.149 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: person_intrusion
2025-07-28 10:43:23.150 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 10:43:23.151 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 10:43:23.151 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 10:43:23.151 | a5a62f2ef22343fba553fb0b8a3e8e82 | INFO     | module_stream.controller.algorithm_standard_controller:get_algorithms_with_info:428 - 获取算法信息列表成功，共3个算法
2025-07-28 10:43:36.239 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 10:43:36.239 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
