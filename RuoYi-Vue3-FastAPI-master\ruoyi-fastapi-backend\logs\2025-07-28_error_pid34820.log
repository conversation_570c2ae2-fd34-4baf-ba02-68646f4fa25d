2025-07-28 12:02:12.191 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:02:12.192 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:02:12.216 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:02:12.217 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:02:12.218 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:02:12.256 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:02:12.283 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:02:12.283 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:05:30.934 | d7ddc20841eb48febd78187352b3460b | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 12:05:30.959 | 296a4bb6c3b5412bb6698ee98a69a94b | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 12:05:31.133 | 449faa552da04732a17234d67ee35433 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:09:04.880 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:09:04.880 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
