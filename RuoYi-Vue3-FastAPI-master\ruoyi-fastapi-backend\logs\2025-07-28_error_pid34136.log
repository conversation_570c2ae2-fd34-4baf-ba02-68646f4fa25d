2025-07-28 15:09:29.108 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 15:09:29.108 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 15:09:29.146 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 15:09:29.146 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 15:09:29.147 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 15:09:29.182 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 15:09:29.195 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 15:09:29.196 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 15:10:10.355 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:list_available_algorithms:792 - 找到 3 个可用算法
2025-07-28 15:10:10.356 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: car_counting
2025-07-28 15:10:10.356 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 15:10:10.357 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 15:10:10.357 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: car_counting
2025-07-28 15:10:10.358 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: person_counting
2025-07-28 15:10:10.358 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 15:10:10.359 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 15:10:10.359 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_counting
2025-07-28 15:10:10.360 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_metadata_from_yaml:627 - 成功读取算法元数据: person_intrusion
2025-07-28 15:10:10.361 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 15:10:10.361 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 15:10:10.362 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.service.algorithm_config_service:get_algorithm_config_from_zhiquli:655 - 成功读取算法配置: person_intrusion
2025-07-28 15:10:10.362 | f719cd67ccf7485bba3cc140d87bd85b | INFO     | module_stream.controller.algorithm_standard_controller:get_algorithms_with_info:428 - 获取算法信息列表成功，共3个算法
2025-07-28 15:10:39.127 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 15:10:39.127 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
