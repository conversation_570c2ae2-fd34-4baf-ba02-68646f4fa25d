2025-07-28 15:17:48.752 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 15:17:48.753 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 15:17:48.780 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 15:17:48.780 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 15:17:48.781 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 15:17:48.817 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 15:17:48.827 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 15:17:48.827 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 15:18:03.681 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1107: 实体对象 = True
2025-07-28 15:18:03.682 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151128_234.jpg
2025-07-28 15:18:03.682 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151128_234.jpg
2025-07-28 15:18:03.685 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1107
2025-07-28 15:18:03.705 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1106: 实体对象 = True
2025-07-28 15:18:03.706 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151125_916.jpg
2025-07-28 15:18:03.706 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151125_916.jpg
2025-07-28 15:18:03.709 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1106
2025-07-28 15:18:03.711 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1105: 实体对象 = True
2025-07-28 15:18:03.711 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151123_612.jpg
2025-07-28 15:18:03.711 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151123_612.jpg
2025-07-28 15:18:03.713 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1105
2025-07-28 15:18:03.715 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1104: 实体对象 = True
2025-07-28 15:18:03.715 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151121_258.jpg
2025-07-28 15:18:03.715 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151121_258.jpg
2025-07-28 15:18:03.718 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1104
2025-07-28 15:18:03.724 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1103: 实体对象 = True
2025-07-28 15:18:03.725 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151120_747.jpg
2025-07-28 15:18:03.725 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151120_747.jpg
2025-07-28 15:18:03.727 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1103
2025-07-28 15:18:03.728 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1102: 实体对象 = True
2025-07-28 15:18:03.729 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151120_276.jpg
2025-07-28 15:18:03.729 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151120_276.jpg
2025-07-28 15:18:03.731 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1102
2025-07-28 15:18:03.733 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1101: 实体对象 = True
2025-07-28 15:18:03.733 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151119_846.jpg
2025-07-28 15:18:03.733 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151119_846.jpg
2025-07-28 15:18:03.734 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1101
2025-07-28 15:18:03.735 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1100: 实体对象 = True
2025-07-28 15:18:03.736 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151119_439.jpg
2025-07-28 15:18:03.736 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151119_439.jpg
2025-07-28 15:18:03.738 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1100
2025-07-28 15:18:03.739 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1099: 实体对象 = True
2025-07-28 15:18:03.740 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151119_066.jpg
2025-07-28 15:18:03.740 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151119_066.jpg
2025-07-28 15:18:03.741 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1099
2025-07-28 15:18:03.743 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1098: 实体对象 = True
2025-07-28 15:18:03.744 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151118_700.jpg
2025-07-28 15:18:03.744 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151118_700.jpg
2025-07-28 15:18:03.745 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1098
2025-07-28 15:18:03.822 | f623b8e8e134463b91f2b39db338e096 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除10条记录，删除10个截图文件
2025-07-28 15:18:03.860 | cda9cf6912c647aab368202527403199 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:18:08.829 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1097: 实体对象 = True
2025-07-28 15:18:08.830 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151118_383.jpg
2025-07-28 15:18:08.830 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151118_383.jpg
2025-07-28 15:18:08.831 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1097
2025-07-28 15:18:08.833 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1096: 实体对象 = True
2025-07-28 15:18:08.833 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151118_101.jpg
2025-07-28 15:18:08.834 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151118_101.jpg
2025-07-28 15:18:08.835 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1096
2025-07-28 15:18:08.836 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1095: 实体对象 = True
2025-07-28 15:18:08.837 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250728_151117_840.jpg
2025-07-28 15:18:08.837 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250728_151117_840.jpg
2025-07-28 15:18:08.838 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1095
2025-07-28 15:18:08.852 | 177c3cf9a12d4e9d800e693bbabe72c7 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除3条记录，删除3个截图文件
2025-07-28 15:18:08.887 | 70e2b80456bd43e0a52a92b2bc0af40b | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 15:18:16.157 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 15:18:16.157 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:18:16.157 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:18:16.158 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:18:16.158 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:18:16.158 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:18:16.158 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:18:16.159 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:18:16.159 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:18:16.159 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:18:16.172 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:18:16.172 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:18:16.172 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:18:16.172 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:18:16.175 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:18:16.175 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:18:16.175 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:18:16.175 | a86ea1d697394e6abdf16e360f396122 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 15:18:20.984 | a29963f567c444188be5d34710655194 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 15:18:21.001 | 4392d30a18ed45969b4c6b8136cdc95e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 15:18:21.144 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 15:18:21.144 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 15:18:21.144 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 15:18:21.144 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:18:21.144 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 15:18:21.145 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 15:18:21.145 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 15:18:21.145 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 15:18:21.145 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 15:18:21.146 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 15:18:21.150 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-28 15:18:21.150 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 15:18:21.150 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-28 15:18:21.150 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-28 15:18:21.152 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 15:18:21.152 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-28 15:18:21.152 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 15:18:21.153 | c66e66cd361741968a0925c4f59b1fb0 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 15:18:26.648 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 15:18:26.648 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
