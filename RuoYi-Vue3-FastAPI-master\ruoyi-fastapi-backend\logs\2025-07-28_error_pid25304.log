2025-07-28 12:18:09.541 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:18:09.541 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:18:09.566 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:18:09.566 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:18:09.568 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:18:09.599 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:18:09.609 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:18:09.609 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:18:11.326 | 2472f0b3e76c4ac28ca99c97da664488 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 12:18:11.361 | 56488e208cef414cbe0557b890e18c41 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 12:18:11.534 | 8251065ffe59446f9aa618cdc0f7a56a | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:18:19.701 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:18:19.701 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
