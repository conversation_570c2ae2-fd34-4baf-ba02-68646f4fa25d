2025-07-28 12:21:42.888 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:21:42.889 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:21:42.916 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:21:42.916 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:21:42.917 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:21:42.949 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:21:42.959 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:21:42.959 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:22:01.175 | 623539a26a53401aac2112865902b960 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 12:22:01.210 | a280e3564deb48a2809a10ce873c9cb4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 12:22:01.389 | 12367645c2574c60ad3af8178978df31 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:23:00.834 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:23:00.834 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
