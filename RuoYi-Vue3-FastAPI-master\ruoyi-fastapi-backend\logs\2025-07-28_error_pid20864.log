2025-07-28 12:01:57.063 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:01:57.064 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:01:57.097 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:01:57.097 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:01:57.099 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:01:57.133 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:01:57.171 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:01:57.171 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:01:58.070 | c6a9c81afe1a411e8619e4c068d9f1df | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 12:01:58.071 | c6a9c81afe1a411e8619e4c068d9f1df | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 12:01:58.077 | c6a9c81afe1a411e8619e4c068d9f1df | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 12:02:00.228 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 12:02:00.228 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 12:02:00.228 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 12:02:00.229 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 12:02:00.229 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 12:02:00.229 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 12:02:00.229 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 12:02:00.229 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 12:02:00.230 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 12:02:00.230 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 12:02:00.237 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 12:02:00.238 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 12:02:00.238 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 12:02:00.238 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 12:02:00.240 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 12:02:00.241 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 12:02:00.241 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 12:02:00.241 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 12:02:00.242 | c0b4f0e77930441cbd7df5d9d5aacf82 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 12:02:02.877 | d5e5b8ed09eb4fb491100427d10fde51 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:02:05.808 | 1ec4223bb7ec404c93df0088a08a6344 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 12:02:05.843 | 00bfaa682ed441a7b16a610d86d13283 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 12:02:06.009 | 9430fc029bab4be9a0c78505886b3eab | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:02:09.943 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:02:09.943 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
