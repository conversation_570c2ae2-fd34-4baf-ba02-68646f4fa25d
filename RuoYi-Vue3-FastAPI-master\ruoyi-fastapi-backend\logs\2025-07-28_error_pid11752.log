2025-07-28 12:01:42.740 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:01:42.740 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:01:42.770 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:01:42.770 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:01:42.772 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:01:42.802 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:01:42.822 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:01:42.822 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:01:49.302 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-28 12:01:49.303 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:stop_task:332 - 任务 12 不在运行任务列表中
2025-07-28 12:01:49.303 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 12:01:49.304 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-28 12:01:49.304 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-28 12:01:49.304 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-28 12:01:49.318 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-28 12:01:49.318 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-28 12:01:49.319 | 594d239f84df46d2a827377210210055 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 12:01:54.403 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:01:54.404 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
