2025-07-28 11:21:35.514 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 11:21:35.514 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 11:21:35.541 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 11:21:35.541 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 11:21:35.542 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 11:21:35.574 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 11:21:35.585 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 11:21:35.585 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 11:22:16.449 | 4a150ef62a5c42daaaaa5016361c36c6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 11:22:16.465 | 9686c276c0f44532b79c7e783c4a25a4 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 11:22:16.671 | 913b74f506ad42c8b20472cb6be31917 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 11:22:26.890 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:145 - 检查告警记录 951: 实体对象 = True
2025-07-28 11:22:26.891 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:237 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_100014_713.jpg
2025-07-28 11:22:26.891 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:153 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_100014_713.jpg
2025-07-28 11:22:26.894 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:165 - 成功删除告警记录 951
2025-07-28 11:22:26.895 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:145 - 检查告警记录 950: 实体对象 = True
2025-07-28 11:22:26.896 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:237 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_100011_272.jpg
2025-07-28 11:22:26.896 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:153 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_100011_272.jpg
2025-07-28 11:22:26.897 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:165 - 成功删除告警记录 950
2025-07-28 11:22:26.899 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:145 - 检查告警记录 949: 实体对象 = True
2025-07-28 11:22:26.899 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:237 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_100011_068.jpg
2025-07-28 11:22:26.899 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:153 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_100011_068.jpg
2025-07-28 11:22:26.900 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:165 - 成功删除告警记录 949
2025-07-28 11:22:26.901 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:145 - 检查告警记录 948: 实体对象 = True
2025-07-28 11:22:26.902 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:237 - 成功删除截图文件: uploads\alerts\20250728\alert_12_8_20250728_100010_851.jpg
2025-07-28 11:22:26.902 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:153 - 成功删除告警截图: /uploads/alerts/20250728/alert_12_8_20250728_100010_851.jpg
2025-07-28 11:22:26.903 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.service.alert_service:delete_alert_services:165 - 成功删除告警记录 948
2025-07-28 11:22:26.906 | af5114dee61d464885e638245de8ab6d | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除4条记录，删除4个截图文件
2025-07-28 11:22:26.938 | 57af09213c7842d990c9688400311040 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 11:22:30.141 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 11:22:30.141 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 11:22:30.141 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 11:22:30.141 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 11:22:30.142 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 11:22:30.142 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 11:22:30.142 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 11:22:30.142 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 11:22:30.143 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 11:22:30.143 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 11:22:30.148 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 11:22:30.148 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 11:22:30.148 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 11:22:30.148 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 11:22:30.150 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 11:22:30.151 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 11:22:30.152 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 11:22:30.152 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 11:22:30.152 | c8e063b6d4924a01a79d51a2cca93e3a | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 11:22:33.371 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-28 11:22:33.373 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:stop_task:332 - 任务 12 不在运行任务列表中
2025-07-28 11:22:33.373 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-28 11:22:33.374 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-28 11:22:33.374 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-28 11:22:33.374 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-28 11:22:33.389 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-28 11:22:33.389 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-28 11:22:33.389 | 332f6a270a8d42c5819441819968fbe7 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-28 11:22:33.407 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 11:22:33.407 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 11:22:33.407 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 11:22:33.408 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 11:22:33.408 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 11:22:33.408 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 11:22:33.408 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 11:22:33.408 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 11:22:33.409 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 11:22:33.409 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 11:22:33.413 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 11:22:33.413 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 11:22:33.413 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 11:22:33.413 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 11:22:33.414 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 11:22:33.415 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 11:22:33.416 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 11:22:33.416 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 11:22:33.416 | 0e7401363c4840c587f408b048fb5659 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 11:22:38.449 | 5624ed10b2f9406d8b42cad422c5a092 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 11:22:40.458 | ff047f044c2e4c448e3735bfdb152deb | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 11:22:40.458 | ff047f044c2e4c448e3735bfdb152deb | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 11:22:40.463 | ff047f044c2e4c448e3735bfdb152deb | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 11:22:41.990 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-28 11:22:41.991 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-28 11:22:41.991 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-28 11:22:41.991 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 11:22:41.991 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-28 11:22:41.992 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-28 11:22:41.992 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-28 11:22:41.992 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-28 11:22:41.992 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-28 11:22:41.992 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-28 11:22:42.002 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-28 11:22:42.002 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-28 11:22:42.003 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-28 11:22:42.003 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-28 11:22:42.005 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-28 11:22:42.008 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-28 11:22:42.008 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-28 11:22:42.008 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-28 11:22:42.008 | 07cec0e1b9ba45829feae56c29358e33 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-28 11:25:30.303 | 9ad0b5b0179d4531bc6b41cb027e4538 | INFO     | module_stream.controller.stream_controller:add_stream_manage_stream:320 - 新增成功
2025-07-28 11:25:30.338 | be44f284ead94521a7b8b47de9cbe3bc | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:282 - 接收到的搜索参数: pageNum=1, pageSize=10, streamName=None, rtspUrl=None, location=None, status=None, isRecording=None
2025-07-28 11:25:30.338 | be44f284ead94521a7b8b47de9cbe3bc | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:297 - 构建的查询对象: {'stream_id': None, 'user_id': 1, 'stream_name': None, 'rtsp_url': None, 'location': None, 'stream_config': None, 'status': None, 'is_recording': None, 'del_flag': None, 'create_by': None, 'create_time': None, 'update_by': None, 'update_time': None, 'remark': None, 'page_num': 1, 'page_size': 10}
2025-07-28 11:25:30.341 | be44f284ead94521a7b8b47de9cbe3bc | INFO     | module_stream.controller.stream_controller:get_stream_manage_stream_list:300 - 获取成功
2025-07-28 11:26:25.212 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 11:26:25.212 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
