2025-07-28 12:27:17.310 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:27:17.311 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:27:17.340 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:27:17.340 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:27:17.341 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:27:17.373 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:27:17.399 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:27:17.399 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:27:55.042 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:27:55.043 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-28 15:21:29.311 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 15:21:29.311 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 15:21:29.342 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 15:21:29.342 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 15:21:29.343 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 15:21:29.377 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 15:21:29.410 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 15:21:29.410 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 15:21:58.184 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 15:21:58.184 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
