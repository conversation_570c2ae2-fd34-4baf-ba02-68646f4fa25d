2025-07-28 12:26:53.296 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 12:26:53.297 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 12:26:53.324 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 12:26:53.324 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 12:26:53.325 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 12:26:53.361 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 12:26:53.372 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 12:26:53.372 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 12:26:57.097 | 9d20b463830d4287bab9cbae1181567a | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 12:26:57.117 | 114fd96d5bb94e6195ccf96fe5b9e9b9 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 12:26:57.304 | 560372af94dd4151bc22c89f0f67da85 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-28 12:27:15.225 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 12:27:15.226 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
