2025-07-28 10:30:07.144 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-28 10:30:07.144 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-28 10:30:07.171 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-28 10:30:07.171 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-28 10:30:07.173 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-28 10:30:07.205 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-28 10:30:07.215 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-28 10:30:07.215 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-28 10:31:16.554 | d29c8ee118e649d1aecca729394d5b28 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:31:16.569 | ea7578d330654aadb4b58bddd9860aeb | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:31:16.696 | 88339222bf184913840fc9f660e14715 | ERROR    | exceptions.handle:exception_handler:136 - 'dict' object has no attribute 'stream_id'
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 324
               │     └ 3
               └ <function _main at 0x0000020FB18556C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 324
           │    └ <function BaseProcess._bootstrap at 0x0000020FB1564900>
           └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000020FB1563E20>
    └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000020FB153ECF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
    │    └ <function subprocess_started at 0x0000020FB3A90040>
    └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000020FE1C6C6E0>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000020FB3A87060>
           │       │   └ <uvicorn.server.Server object at 0x0000020FE1C6C6E0>
           │       └ <function run at 0x0000020FB3730A40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020FE1F9FA00>
           │      └ <function Runner.run at 0x0000020FB3770040>
           └ <asyncio.runners.Runner object at 0x0000020FE1C6D160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-pa...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020FB3769A80>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020FE1C6D160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020FB37699E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020FB376B7E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020FB32F14E0>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020FE1C6EE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020FE2...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000020FE15FFCB0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020FE1C6EE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020FE2...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020FE2...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020FE21D8590>
          └ <fastapi.applications.FastAPI object at 0x0000020FE15FFCB0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020FE21D7BA0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000020FE21D8440>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020FE21D8590>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\middlewares\trace_middleware\middle.py", line 48, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000020FE22ECC20>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000020FE21D82F0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000020FE21D8440>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000020FE22ECC20>
          │         │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000020FE219E490>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000020FDF403E20>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000020FE219E490>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020FE21D81A0>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000020FE219E490>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000020FE219E490>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020FE21D8050>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020FE21D81A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000020FE219E490>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000020FE219AB10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000020FE1C65130>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020FE21D8050>
          └ <function wrap_app_handling_exceptions at 0x0000020FB4A66520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22ECEA0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000020FE1C65130>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22ECEA0>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000020FE1C65130>>
          └ <fastapi.routing.APIRouter object at 0x0000020FE1C65130>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22ECEA0>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000020FB4A67D80>
          └ APIRoute(path='/surveillance/alert/list', name='get_alert_manage_alert_list', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22ECEA0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000020FE1FE0360>
          └ APIRoute(path='/surveillance/alert/list', name='get_alert_manage_alert_list', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22ECEA0>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000020FE223E0F0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000020FE22EC680>
          └ <function wrap_app_handling_exceptions at 0x0000020FB4A66520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22EF2E0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000020FE22EF6A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000020FE22EC680>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000020FE223E0F0>
                     └ <function get_request_handler.<locals>.app at 0x0000020FE1FE0220>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000020FB4A65D00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'query_db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000020FE223E8D0>, 'current_user': CurrentUserModel(per...
                 │         └ <function get_alert_manage_alert_list at 0x0000020FE1BC2E80>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(1), name='pageNum', mode='validation'), ModelField(field_...

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_alert\controller\alert_controller.py", line 69, in get_alert_manage_alert_list
    alert_page_query_result = await AlertService.get_alert_list_services(query_db, alert_page_query, is_page=True)
                                    │            │                       │         └ AlertPageQueryModel(alert_id=None, user_id=1, task_id=None, stream_id=None, model_id=None, algorithm_type=None, alert_type=No...
                                    │            │                       └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000020FE223E8D0>
                                    │            └ <classmethod(<function AlertService.get_alert_list_services at 0x0000020FE1BC1760>)>
                                    └ <class 'module_alert.service.alert_service.AlertService'>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_alert\service\alert_service.py", line 36, in get_alert_list_services
    await cls._add_stream_and_task_names(query_db, alert_list_result.rows)
          │   │                          │         │                 └ [{'taskId': 12, 'bboxInfo': '{"hit": true, "message": "\\u8f66\\u8f86\\u8ba1\\u6570\\u544a\\u8b66\\uff0c\\u6709\\u8f66\\u8f86...
          │   │                          │         └ PageResponseModel(rows=[{'taskId': 12, 'bboxInfo': '{"hit": true, "message": "\\u8f66\\u8f86\\u8ba1\\u6570\\u544a\\u8b66\\uff...
          │   │                          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000020FE223E8D0>
          │   └ <classmethod(<function AlertService._add_stream_and_task_names at 0x0000020FE1BC3380>)>
          └ <class 'module_alert.service.alert_service.AlertService'>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_alert\service\alert_service.py", line 57, in _add_stream_and_task_names
    SurveillanceStream.stream_id == alert.stream_id
    │                  │            └ {'taskId': 12, 'bboxInfo': '{"hit": true, "message": "\\u8f66\\u8f86\\u8ba1\\u6570\\u544a\\u8b66\\uff0c\\u6709\\u8f66\\u8f86\...
    │                  └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x0000020FE19B8220>
    └ <class 'module_stream.entity.do.stream_do.SurveillanceStream'>

AttributeError: 'dict' object has no attribute 'stream_id'
2025-07-28 10:32:12.638 | ba9d1d810431455bb0c3e26c1bf70b51 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-28 10:32:12.654 | 29dcc1517a414238957861a9f53ad0e1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-28 10:32:12.781 | 9bdfadfb47da4f5ca1c0bf4b1673b3de | ERROR    | exceptions.handle:exception_handler:136 - 'dict' object has no attribute 'stream_id'
Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 324
               │     └ 3
               └ <function _main at 0x0000020FB18556C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 324
           │    └ <function BaseProcess._bootstrap at 0x0000020FB1564900>
           └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x0000020FB1563E20>
    └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x0000020FB153ECF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
    │    └ <function subprocess_started at 0x0000020FB3A90040>
    └ <SpawnProcess name='SpawnProcess-6' parent=32552 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x0000020FE1C6C6E0>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x0000020FB3A87060>
           │       │   └ <uvicorn.server.Server object at 0x0000020FE1C6C6E0>
           │       └ <function run at 0x0000020FB3730A40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000020FE1F9FA00>
           │      └ <function Runner.run at 0x0000020FB3770040>
           └ <asyncio.runners.Runner object at 0x0000020FE1C6D160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-pa...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000020FB3769A80>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000020FE1C6D160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x0000020FB37699E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000020FB376B7E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000020FB32F14E0>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020FE1C6EE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020FE2...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000020FE15FFCB0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000020FE1C6EE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020FE2...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020FE2...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020FE21D8590>
          └ <fastapi.applications.FastAPI object at 0x0000020FE15FFCB0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000020FE1FE3100>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000020...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000020FE21D8440>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000020FE21D8590>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\middlewares\trace_middleware\middle.py", line 48, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000020FE234A3E0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x0000020FE21D82F0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x0000020FE21D8440>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x0000020FE234A3E0>
          │         │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000020FE2341910>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x0000020FDF403E20>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x0000020FE2341910>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020FE21D81A0>
          └ <starlette.middleware.gzip.GZipResponder object at 0x0000020FE2341910>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000020FE2341910>>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020FE21D8050>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000020FE21D81A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x0000020FE2341910>>
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000020FE21FF2F0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000020FE1C65130>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000020FE21D8050>
          └ <function wrap_app_handling_exceptions at 0x0000020FB4A66520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22EFD80>
          │   │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000020FE1C65130>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22EFD80>
          │    │                │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000020FE1C65130>>
          └ <fastapi.routing.APIRouter object at 0x0000020FE1C65130>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22EFD80>
          │     │      │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x0000020FB4A67D80>
          └ APIRoute(path='/surveillance/alert/list', name='get_alert_manage_alert_list', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22EFD80>
          │    │   │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000020FE1FE0360>
          └ APIRoute(path='/surveillance/alert/list', name='get_alert_manage_alert_list', methods=['GET'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22EFD80>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000020FE2238F30>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000020FE22EF6A0>
          └ <function wrap_app_handling_exceptions at 0x0000020FB4A66520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000020FE22ECFE0>
          │   │      └ <function RequestResponseCycle.receive at 0x0000020FE234A8E0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000020FE22EF6A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000020FE2238F30>
                     └ <function get_request_handler.<locals>.app at 0x0000020FE1FE0220>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000020FB4A65D00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'query_db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000020FE2361F50>, 'current_user': CurrentUserModel(per...
                 │         └ <function get_alert_manage_alert_list at 0x0000020FE1BC2E80>
                 └ Dependant(path_params=[], query_params=[ModelField(field_info=Query(1), name='pageNum', mode='validation'), ModelField(field_...

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_alert\controller\alert_controller.py", line 69, in get_alert_manage_alert_list
    alert_page_query_result = await AlertService.get_alert_list_services(query_db, alert_page_query, is_page=True)
                                    │            │                       │         └ AlertPageQueryModel(alert_id=None, user_id=1, task_id=None, stream_id=None, model_id=None, algorithm_type=None, alert_type=No...
                                    │            │                       └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000020FE2361F50>
                                    │            └ <classmethod(<function AlertService.get_alert_list_services at 0x0000020FE1BC1760>)>
                                    └ <class 'module_alert.service.alert_service.AlertService'>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_alert\service\alert_service.py", line 36, in get_alert_list_services
    await cls._add_stream_and_task_names(query_db, alert_list_result.rows)
          │   │                          │         │                 └ [{'taskId': 12, 'bboxInfo': '{"hit": true, "message": "\\u8f66\\u8f86\\u8ba1\\u6570\\u544a\\u8b66\\uff0c\\u6709\\u8f66\\u8f86...
          │   │                          │         └ PageResponseModel(rows=[{'taskId': 12, 'bboxInfo': '{"hit": true, "message": "\\u8f66\\u8f86\\u8ba1\\u6570\\u544a\\u8b66\\uff...
          │   │                          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000020FE2361F50>
          │   └ <classmethod(<function AlertService._add_stream_and_task_names at 0x0000020FE1BC3380>)>
          └ <class 'module_alert.service.alert_service.AlertService'>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_alert\service\alert_service.py", line 57, in _add_stream_and_task_names
    SurveillanceStream.stream_id == alert.stream_id
    │                  │            └ {'taskId': 12, 'bboxInfo': '{"hit": true, "message": "\\u8f66\\u8f86\\u8ba1\\u6570\\u544a\\u8b66\\uff0c\\u6709\\u8f66\\u8f86\...
    │                  └ <sqlalchemy.orm.attributes.InstrumentedAttribute object at 0x0000020FE19B8220>
    └ <class 'module_stream.entity.do.stream_do.SurveillanceStream'>

AttributeError: 'dict' object has no attribute 'stream_id'
2025-07-28 10:35:01.175 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-28 10:35:01.176 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
