"""
通用PyTorch适配的RknnModel基类
适用于所有智驱力算法，完全替代RKNN，保持原有逻辑不变
"""

import cv2
import numpy as np
import torch
import sys
import os
from pathlib import Path

# 添加YOLOv5路径 - 使用绝对路径
yolov5_absolute_path = "D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/Yolov5"
if os.path.exists(yolov5_absolute_path):
    sys.path.insert(0, yolov5_absolute_path)
    print(f"[INFO] 添加YOLOv5路径: {yolov5_absolute_path}")
else:
    print(f"[WARNING] YOLOv5路径不存在: {yolov5_absolute_path}")

class Logger:
    """简化的Logger类"""
    def info(self, msg):
        print(f"[INFO] {msg}")

    def error(self, msg):
        print(f"[ERROR] {msg}")

    def exception(self, msg):
        print(f"[EXCEPTION] {msg}")
        import traceback
        traceback.print_exc()

LOGGER = Logger()

class RknnModel:
    """
    通用PyTorch适配的RknnModel基类
    完全替代RKNN，保持智驱力原有接口和逻辑不变
    """
    def __init__(self, acc_id, name, conf, model_names):
        self.acc_id = acc_id
        self.name = name
        self.conf = conf
        self.model_names = model_names
        self.models = {}
        self.status = False
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self._load_models()
        
        # 调用子类的参数加载方法
        if hasattr(self, '_load_args'):
            success = self._load_args(conf)
            if not success:
                LOGGER.error("参数加载失败")
                self.status = False

    def _load_models(self):
        """加载PyTorch模型 - 仅支持torch.hub在线加载"""
        try:
            for model_name in self.model_names:
                try:
                    LOGGER.info(f"从torch.hub加载YOLOv5模型: {model_name}")

                    # 根据算法类型选择合适的模型
                    if 'person' in self.name.lower():
                        # 人员相关算法使用yolov5s
                        model = torch.hub.load("ultralytics/yolov5", "yolov5s", pretrained=True)
                    elif 'car' in self.name.lower() or 'vehicle' in self.name.lower():
                        # 车辆相关算法使用yolov5m（更好的车辆检测）
                        model = torch.hub.load("ultralytics/yolov5", "yolov5m", pretrained=True)
                    else:
                        # 默认使用yolov5s
                        model = torch.hub.load("ultralytics/yolov5", "yolov5s", pretrained=True)

                    model.eval()
                    model.to(self.device)

                    # 设置模型参数
                    model.conf = self.conf_thres  # 置信度阈值
                    model.iou = self.nms_thres    # NMS阈值

                    # 根据算法类型设置目标类别
                    if 'person' in self.name.lower():
                        # 只检测人员 (class 0)
                        model.classes = [0]
                    elif 'car' in self.name.lower() or 'vehicle' in self.name.lower():
                        # 检测车辆相关类别 (car, motorcycle, bus, truck)
                        model.classes = [2, 3, 5, 7]

                    self.models[model_name] = model
                    LOGGER.info(f"成功加载YOLOv5模型: {model_name}")

                except Exception as e:
                    LOGGER.error(f"torch.hub加载模型失败: {e}")
                    # 如果网络加载失败，创建虚拟模型用于离线测试
                    LOGGER.warning(f"创建虚拟模型用于离线测试: {model_name}")
                    self.models[model_name] = self._create_dummy_model()

            self.status = True

        except Exception as e:
            LOGGER.exception(f"模型加载失败: {e}")
            self.status = False

    def _create_missing_modules(self):
        """创建缺失的模块来解决导入问题"""
        import sys
        import types

        # 创建utils模块
        if 'utils' not in sys.modules:
            utils_module = types.ModuleType('utils')
            sys.modules['utils'] = utils_module

        # 创建utils.dataloaders模块
        if 'utils.dataloaders' not in sys.modules:
            dataloaders_module = types.ModuleType('utils.dataloaders')
            # 添加一些常用的类和函数
            dataloaders_module.LoadImages = type('LoadImages', (), {})
            dataloaders_module.LoadStreams = type('LoadStreams', (), {})
            dataloaders_module.LoadWebcam = type('LoadWebcam', (), {})
            dataloaders_module.LoadScreenshots = type('LoadScreenshots', (), {})
            dataloaders_module.create_dataloader = lambda *args, **kwargs: None
            # 添加缺失的函数
            dataloaders_module.exif_transpose = lambda x: x
            dataloaders_module.letterbox = lambda x, *args, **kwargs: (x, 1.0, (0, 0))
            dataloaders_module.img2label_paths = lambda x: x
            sys.modules['utils.dataloaders'] = dataloaders_module

        # 创建其他可能需要的utils子模块
        for submodule in ['utils.general', 'utils.torch_utils', 'utils.plots', 'utils.metrics', 'utils.augmentations']:
            if submodule not in sys.modules:
                temp_module = types.ModuleType(submodule)
                # 添加一些常用的函数
                temp_module.check_img_size = lambda x, s=32: x
                temp_module.non_max_suppression = lambda *args, **kwargs: []
                temp_module.scale_coords = lambda *args, **kwargs: None
                temp_module.xyxy2xywh = lambda x: x
                temp_module.xywh2xyxy = lambda x: x
                temp_module.clip_coords = lambda *args, **kwargs: None
                temp_module.check_requirements = lambda *args, **kwargs: None
                temp_module.check_file = lambda x: x
                temp_module.increment_path = lambda x, *args, **kwargs: x
                temp_module.colorstr = lambda *args, **kwargs: str(args[0]) if args else ""
                temp_module.make_divisible = lambda x, divisor=32: x
                temp_module.time_sync = lambda: 0.0
                temp_module.check_suffix = lambda file, suffix, msg='': file
                temp_module.check_dataset = lambda data: data
                temp_module.check_yaml = lambda file: file
                temp_module.check_requirements = lambda requirements='requirements.txt', exclude=(), install=True: None
                temp_module.check_version = lambda current='0.0.0', minimum='0.0.0', name='version', pinned=False, hard=False, verbose=False: True
                temp_module.yaml_load = lambda file='data.yaml', append_filename=False: {'names': ['person']}
                temp_module.yaml_save = lambda file='data.yaml', data=None: None
                temp_module.copy_attr = lambda a, b, include=(), exclude=(): None
                temp_module.select_device = lambda device='', batch_size=None, newline=True: 'cpu'
                temp_module.time_sync = lambda: 0.0
                temp_module.fuse_conv_and_bn = lambda conv, bn: conv
                temp_module.model_info = lambda model, verbose=False, img_size=640: None
                temp_module.scale_img = lambda img, ratio=1.0, same_shape=False, gs=32: img
                temp_module.initialize_weights = lambda model: None
                temp_module.find_modules = lambda model, mclass=None: []
                temp_module.sparsity = lambda model: 0.0
                temp_module.prune = lambda model, amount=0.3: None
                # 添加Annotator类
                class DummyAnnotator:
                    def __init__(self, *args, **kwargs):
                        pass
                    def box_label(self, *args, **kwargs):
                        pass
                    def rectangle(self, *args, **kwargs):
                        pass
                    def text(self, *args, **kwargs):
                        pass
                temp_module.Annotator = DummyAnnotator
                temp_module.Colors = lambda: None
                temp_module.colors = lambda *args, **kwargs: (255, 255, 255)
                temp_module.save_one_box = lambda *args, **kwargs: None
                temp_module.plot_one_box = lambda *args, **kwargs: None
                # 添加LOGGER和ROOT
                import logging
                from pathlib import Path
                temp_module.LOGGER = logging.getLogger(__name__)
                temp_module.ROOT = Path(__file__).parent.parent.parent.parent.parent.parent  # YOLOv5根目录
                sys.modules[submodule] = temp_module

        LOGGER.info("成功创建缺失的模块")

    def _create_dummy_model(self):
        """创建虚拟模型用于测试"""
        class DummyModel:
            def __init__(self):
                self.device = 'cpu'

            def eval(self):
                return self

            def float(self):
                return self

            def to(self, device):
                self.device = device
                return self

            def __call__(self, x):
                # 返回虚拟的YOLOv5格式输出
                batch_size = x.shape[0] if hasattr(x, 'shape') else 1
                # 模拟YOLOv5输出格式: (batch_size, num_detections, 85)
                # 85 = 4(bbox) + 1(conf) + 80(classes)
                dummy_output = torch.zeros((batch_size, 25200, 85))
                return dummy_output

        return DummyModel()

    def _rknn_infer(self, model_name, inputs):
        """
        PyTorch推理接口 - 专门处理torch.hub YOLOv5模型
        """
        if model_name not in self.models:
            raise ValueError(f"模型 {model_name} 未加载")

        model = self.models[model_name]

        with torch.no_grad():
            input_data = inputs[0]

            # 检查是否是torch.hub加载的YOLOv5模型
            if hasattr(model, 'model') and hasattr(model, 'names'):
                # torch.hub YOLOv5模型 - 直接推理
                LOGGER.info("使用torch.hub YOLOv5模型推理")

                # 准备输入数据
                if isinstance(input_data, torch.Tensor):
                    input_data = input_data.cpu().numpy()

                # 确保输入是正确的格式 (H, W, C) uint8
                if input_data.dtype != np.uint8:
                    if input_data.max() <= 1.0:
                        input_data = (input_data * 255).astype(np.uint8)
                    else:
                        input_data = input_data.astype(np.uint8)

                # YOLOv5推理 - 直接返回检测结果
                results = model(input_data)

                # 转换为智驱力期望的格式
                return self._convert_yolov5_to_zhiquli_format(results)

            else:
                # 虚拟模型或其他模型
                LOGGER.info("使用虚拟模型推理")

                # 返回空的3个尺度输出
                return [
                    np.zeros((3, 85, 20, 20)),   # 20x20网格
                    np.zeros((3, 85, 40, 40)),   # 40x40网格
                    np.zeros((3, 85, 80, 80))    # 80x80网格
                ]

    def _convert_yolov5_to_zhiquli_format(self, results):
        """
        将YOLOv5结果转换为智驱力标准的3个尺度输出格式

        Args:
            results: YOLOv5推理结果

        Returns:
            List: 3个尺度的输出 [output1, output2, output3]
        """
        try:
            # 创建3个尺度的空输出 - 智驱力标准格式
            output1 = np.zeros((3, 85, 20, 20))  # 小目标检测
            output2 = np.zeros((3, 85, 40, 40))  # 中等目标检测
            output3 = np.zeros((3, 85, 80, 80))  # 大目标检测

            # 获取检测结果
            if hasattr(results, 'xyxy') and len(results.xyxy) > 0:
                detections = results.xyxy[0].cpu().numpy()  # [x1, y1, x2, y2, conf, class]

                # 将检测结果编码到合适的尺度中
                for i, det in enumerate(detections):
                    if i >= 100:  # 限制最多处理100个检测
                        break

                    x1, y1, x2, y2, conf, cls = det

                    # 计算目标的中心点和尺寸
                    cx = (x1 + x2) / 2
                    cy = (y1 + y2) / 2
                    w = x2 - x1
                    h = y2 - y1

                    # 根据目标大小选择合适的尺度
                    target_size = max(w, h)

                    if target_size < 50:
                        # 小目标 -> 80x80网格
                        grid_size = 80
                        output = output3
                    elif target_size < 150:
                        # 中等目标 -> 40x40网格
                        grid_size = 40
                        output = output2
                    else:
                        # 大目标 -> 20x20网格
                        grid_size = 20
                        output = output1

                    # 计算网格位置
                    grid_x = int(cx / self.img_size * grid_size)
                    grid_y = int(cy / self.img_size * grid_size)
                    anchor_idx = i % 3  # 简化的anchor分配

                    # 确保索引在有效范围内
                    if 0 <= grid_x < grid_size and 0 <= grid_y < grid_size:
                        # 编码检测结果到网格中
                        output[anchor_idx, 0, grid_y, grid_x] = cx / self.img_size * grid_size - grid_x  # x偏移
                        output[anchor_idx, 1, grid_y, grid_x] = cy / self.img_size * grid_size - grid_y  # y偏移
                        output[anchor_idx, 2, grid_y, grid_x] = w / self.img_size * grid_size  # 宽度
                        output[anchor_idx, 3, grid_y, grid_x] = h / self.img_size * grid_size  # 高度
                        output[anchor_idx, 4, grid_y, grid_x] = conf  # 置信度

                        # 类别概率 (one-hot编码)
                        if 0 <= int(cls) < 80:  # COCO数据集有80个类别
                            output[anchor_idx, 5 + int(cls), grid_y, grid_x] = 1.0

            return [output1, output2, output3]

        except Exception as e:
            LOGGER.error(f"转换YOLOv5结果失败: {e}")
            # 返回空的输出
            return [
                np.zeros((3, 85, 20, 20)),
                np.zeros((3, 85, 40, 40)),
                np.zeros((3, 85, 80, 80))
            ]



    def _convert_pytorch_to_rknn_format(self, pytorch_output):
        """
        将PyTorch YOLOv5输出转换为RKNN期望的格式
        """
        try:
            # 去掉batch维度
            if len(pytorch_output.shape) == 3:
                pytorch_output = pytorch_output[0]  # (num_detections, 85)
            
            total_detections, num_classes = pytorch_output.shape
            
            # YOLOv5标准分割比例
            if total_detections == 25200:  # 640x640输入的标准输出
                split_sizes = [19200, 4800, 1200]
                grid_sizes = [80, 40, 20]
            elif total_detections == 8400:  # 可能是其他输入尺寸
                split_sizes = [6300, 1575, 525]
                grid_sizes = [int(np.sqrt(s/3)) for s in split_sizes]
            else:
                # 通用分割方法：按3:1:0.25比例
                size1 = int(total_detections * 0.76)  # ~76%
                size2 = int(total_detections * 0.19)  # ~19%
                size3 = total_detections - size1 - size2  # 剩余
                split_sizes = [size1, size2, size3]
                grid_sizes = [int(np.sqrt(s/3)) for s in split_sizes]
            
            # 分割并重塑输出
            rknn_outputs = []
            start_idx = 0
            
            for split_size, grid_size in zip(split_sizes, grid_sizes):
                end_idx = start_idx + split_size
                
                # 提取这个尺度的检测结果
                scale_output = pytorch_output[start_idx:end_idx, :]  # (split_size, 85)
                
                # 重塑为RKNN期望的格式: (3, 85, grid_size, grid_size)
                try:
                    grid_cells = grid_size * grid_size
                    expected_total = 3 * grid_cells
                    
                    if split_size >= expected_total:
                        # 截取并重塑
                        truncated = scale_output[:expected_total, :]  # (3*grid_cells, 85)
                        temp = truncated.reshape(3, grid_cells, num_classes)  # (3, grid_cells, 85)
                        temp = temp.transpose(0, 2, 1)  # (3, 85, grid_cells)
                        reshaped = temp.reshape(3, num_classes, grid_size, grid_size)  # (3, 85, grid_size, grid_size)
                    else:
                        # 填充
                        padding_size = expected_total - split_size
                        padding = np.zeros((padding_size, num_classes), dtype=np.float32)
                        padded = np.vstack([scale_output, padding])
                        temp = padded.reshape(3, grid_cells, num_classes)
                        temp = temp.transpose(0, 2, 1)
                        reshaped = temp.reshape(3, num_classes, grid_size, grid_size)
                    
                    rknn_outputs.append(reshaped)
                except ValueError:
                    # 备用方案
                    backup_output = np.zeros((3, num_classes, grid_size, grid_size), dtype=np.float32)
                    rknn_outputs.append(backup_output)
                
                start_idx = end_idx
            
            LOGGER.info(f"PyTorch输出转换成功: {pytorch_output.shape} -> {[out.shape for out in rknn_outputs]}")
            return rknn_outputs
            
        except Exception as e:
            LOGGER.exception(f"PyTorch到RKNN格式转换失败: {e}")
            # 返回备用格式
            return [
                np.zeros((3, 85, 80, 80), dtype=np.float32),
                np.zeros((3, 85, 40, 40), dtype=np.float32), 
                np.zeros((3, 85, 20, 20), dtype=np.float32)
            ]

    def _letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114), 
                   auto=True, scaleFill=False, scaleup=True, stride=32, stretch=False):
        """智驱力原有的letterbox预处理方法"""
        shape = img.shape[:2]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:
            r = min(r, 1.0)

        ratio = r, r
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]

        if auto:
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)
        elif scaleFill:
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]

        if stretch:
            img = cv2.resize(img, new_shape, interpolation=cv2.INTER_LINEAR)
            dw = dh = 0
        else:
            dw /= 2
            dh /= 2

            if shape[::-1] != new_unpad:
                img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
            top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
            left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
            img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)

        return img, dw, dh

    def _xywh2xyxy(self, x):
        """坐标转换"""
        y = np.copy(x)
        y[:, 0] = x[:, 0] - x[:, 2] / 2
        y[:, 1] = x[:, 1] - x[:, 3] / 2
        y[:, 2] = x[:, 0] + x[:, 2] / 2
        y[:, 3] = x[:, 1] + x[:, 3] / 2
        return y

    def _nms_boxes(self, boxes, scores):
        """NMS处理"""
        try:
            indices = cv2.dnn.NMSBoxes(
                boxes.tolist(), 
                scores.tolist(), 
                self.conf_thres,
                self.nms_thres
            )
            if len(indices) > 0:
                return indices.flatten()
            else:
                return np.array([])
        except Exception as e:
            LOGGER.exception(f"NMS失败: {e}")
            return np.array([])

    def __del__(self):
        """释放资源"""
        try:
            for model in self.models.values():
                if hasattr(model, 'cpu'):
                    model.cpu()
            self.models.clear()
        except:
            pass
